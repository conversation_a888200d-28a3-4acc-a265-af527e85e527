/**
 * Styling begins
 */
.woocommerce, .woocommerce-page {
  /**
   * Quantity inputs
   */ }
  .woocommerce #quantity input::-webkit-outer-spin-button,
  .woocommerce #quantity input::-webkit-inner-spin-button,
  .woocommerce #content .quantity input::-webkit-outer-spin-button,
  .woocommerce #content .quantity input::-webkit-inner-spin-button, .woocommerce-page #quantity input::-webkit-outer-spin-button,
  .woocommerce-page #quantity input::-webkit-inner-spin-button,
  .woocommerce-page #content .quantity input::-webkit-outer-spin-button,
  .woocommerce-page #content .quantity input::-webkit-inner-spin-button {
    display: none; }
  .woocommerce .quantity, .woocommerce-page .quantity {
    position: relative;
    margin: 0 auto;
    overflow: hidden;
    zoom: 1;
    padding-right: 1.1em;
    display: inline-block;
    /* Hide buttons for opera */ }
    .woocommerce .quantity input.qty, .woocommerce-page .quantity input.qty {
      width: 65px;
      height: 40px;
      float: left;
      padding: 0 20px;
      margin: 0;
      text-align: inherit;
      border: 1px solid #ebebeb;
      border-right: 0;
      font-weight: 700;
      border-radius: 5px 0 0 5px;
      -moz-appearance: textfield;
      /* Hide buttons for Firefox 29 and later */ }
    .woocommerce .quantity noindex:-o-prefocus, .woocommerce .quantity input[type=number], .woocommerce-page .quantity noindex:-o-prefocus, .woocommerce-page .quantity input[type=number] {
      padding-right: 1.2em; }
    .woocommerce .quantity .plus,
    .woocommerce .quantity .minus, .woocommerce-page .quantity .plus,
    .woocommerce-page .quantity .minus {
      display: block;
      padding: 0;
      margin: 0;
      font-size: 16px;
      position: absolute;
      text-align: center;
      width: 30px;
      height: 20px;
      text-decoration: none;
      overflow: visible;
      text-decoration: none;
      font-weight: 700;
      cursor: pointer;
      color: #4c4c4c;
      border: 1px solid #ebebeb;
      background-color: #ebebeb;
      text-shadow: none;
      line-height: 1;
      background: #fff; }
      .woocommerce .quantity .plus:hover,
      .woocommerce .quantity .minus:hover, .woocommerce-page .quantity .plus:hover,
      .woocommerce-page .quantity .minus:hover {
        background-color: #88c74a;
        color: #fff; }
    .woocommerce .quantity .plus, .woocommerce-page .quantity .plus {
      top: 0;
      right: 0;
      border-bottom: 0;
      border-radius: 0 5px 0 0; }
    .woocommerce .quantity .minus, .woocommerce-page .quantity .minus {
      bottom: 0;
      right: 0;
      border-top: 0;
      border-radius: 0 0 5px 0; }
.woocommerce-page .quantity .plus:hover,
.woocommerce-page .quantity .minus:hover{
  border-color: #88c74a;
}