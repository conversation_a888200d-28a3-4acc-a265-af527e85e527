<?php
/**
 * Flavio Theme Course Filter Widgets
 * 
 * Consolidated widget classes for course filtering functionality
 * Compatible with PHP 8+ and WordPress 6+
 * 
 * @package Flavio_Theme
 * @version 1.0.0
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly
}

/**
 * Helper function to get course levels
 * Fallback for lp_course_level() if LearnPress is not available
 */
if ( ! function_exists( 'flavio_theme_get_course_levels' ) ) {
    function flavio_theme_get_course_levels() {
        // Try to use LearnPress function first
        if ( function_exists( 'lp_course_level' ) ) {
            return lp_course_level();
        }
        
        // Fallback levels if LearnPress is not available
        return array(
            '' => esc_html__( 'All Levels', 'flavio-theme' ),
            'beginner' => esc_html__( 'Beginner', 'flavio-theme' ),
            'intermediate' => esc_html__( 'Intermediate', 'flavio-theme' ),
            'expert' => esc_html__( 'Expert', 'flavio-theme' ),
        );
    }
}

/**
 * Helper function to get course instructors
 */
if ( ! function_exists( 'flavio_theme_get_course_instructors' ) ) {
    function flavio_theme_get_course_instructors( $limit = '' ) {
        $args = array(
            'role__in'     => array( 'lp_teacher' ),
            'orderby'      => 'login',
            'order'        => 'ASC',
            'number'       => $limit
        ); 
        $instructors = get_users( $args );
        return $instructors;
    }
}

/**
 * Helper function for query string form fields
 */
if ( ! function_exists( 'flavio_theme_query_string_form_fields' ) ) {
    function flavio_theme_query_string_form_fields( $values = null, $exclude = array(), $current_key = '', $return = false ) {
        if ( is_null( $values ) ) {
            $values = $_GET; // WPCS: input var ok, CSRF ok.
        } elseif ( is_string( $values ) ) {
            $url_parts = wp_parse_url( $values );
            $values    = array();

            if ( ! empty( $url_parts['query'] ) ) {
                parse_str( $url_parts['query'], $values );
            }
        }
        $html = '';

        foreach ( $values as $key => $value ) {
            if ( in_array( $key, $exclude, true ) ) {
                continue;
            }
            if ( $current_key ) {
                $key = $current_key . '[' . $key . ']';
            }
            if ( is_array( $value ) ) {
                $html .= flavio_theme_query_string_form_fields( $value, $exclude, $key, true );
            } else {
                $html .= '<input type="hidden" name="' . esc_attr( $key ) . '" value="' . esc_attr( wp_unslash( $value ) ) . '" />';
            }
        }

        if ( $return ) {
            return $html;
        }

        echo trim($html); // WPCS: XSS ok.
    }
}

/**
 * Course Filter Rating Widget
 */
class Flavio_Theme_Widget_Course_Filter_Rating extends WP_Widget {
    
    public function __construct() {
        parent::__construct(
            'flavio_theme_course_filter_rating',
            esc_html__('LearnPress Courses:: Filter Ratings', 'flavio-theme'),
            array( 'description' => esc_html__( 'Show list of course filter rating', 'flavio-theme' ), )
        );
        $this->widgetName = 'courses_filter_rating';
    }

    public function widget( $args, $instance ) {
        $this->render_rating_widget( $args, $instance );
    }
    
    public function form( $instance ) {
        $defaults = array(
            'title' => 'Ratings',
            'layout' => 'list',
        );
        $instance = wp_parse_args( (array) $instance, $defaults );

        $options = array(
            'select' => esc_html__('Select', 'flavio-theme'), 
            'list' => esc_html__('List', 'flavio-theme')
        );
        ?>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id( 'title' )); ?>"><?php esc_html_e( 'Title:', 'flavio-theme' ); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id( 'title' )); ?>" name="<?php echo esc_attr($this->get_field_name( 'title' )); ?>" type="text" value="<?php echo esc_attr( $instance['title'] ); ?>" />
        </p>

        <p>
            <label for="<?php echo esc_attr($this->get_field_id('layout')); ?>">
                <?php echo esc_html__('Layout:', 'flavio-theme' ); ?>
            </label>
            <br>
            <select id="<?php echo esc_attr($this->get_field_id('layout')); ?>" name="<?php echo esc_attr($this->get_field_name('layout')); ?>">
                <?php foreach ($options as $key => $value) { ?>
                    <option value="<?php echo esc_attr( $key ); ?>" <?php selected($instance['layout'],$key); ?> ><?php echo esc_html( $value ); ?></option>
                <?php } ?>
            </select>
        </p>
        <?php
    }

    public function update( $new_instance, $old_instance ) {
        $instance = array();
        $instance['title'] = ( ! empty( $new_instance['title'] ) ) ? strip_tags( $new_instance['title'] ) : '';
        $instance['layout'] = ( ! empty( $new_instance['layout'] ) ) ? strip_tags( $new_instance['layout'] ) : '';
        return $instance;
    }

    private function render_rating_widget( $args, $instance ) {
        if ( ! defined( 'LP_COURSE_CPT' ) ) {
            return;
        }

        extract( $args );
        extract( $instance );
        
        echo trim($before_widget);
        $title = apply_filters('widget_title', $instance['title']);

        if ( $title ) {
            echo trim($before_title) . trim( $title ) . $after_title;
        }

        $courses_page_id = learn_press_get_page_id( 'courses' );
        $url = get_permalink($courses_page_id);
        $selected = isset($_GET['filter-rating']) ? $_GET['filter-rating'] : '';
        ?>
        <div class="filter-rating-widget">
            <form action="<?php echo esc_url($url); ?>" method="get">
                <?php if ( $layout == 'select' ) { ?>
                    <select name="filter-rating" class="filter-rating">
                        <option value=""><?php esc_html_e('All Ratings', 'flavio-theme'); ?></option>
                        <?php for ($i=5; $i >= 1; $i--) { ?>
                            <option value="<?php echo esc_attr($i); ?>" <?php selected($selected, $i); ?>><?php echo esc_html($i); ?>+</option>
                        <?php } ?>
                    </select>
                <?php } else { ?>
                    <ul class="rating-list course-list-check">
                        <?php for ($i=5; $i >= 1; $i--) { ?>
                            <?php
                            $meta_query = array(
                                array(
                                    'key' => '_average_rating',
                                    'value' => $i,
                                    'compare' => '>=',
                                ),
                                array(
                                    'key' => '_average_rating',
                                    'value' => ($i + 1),
                                    'compare' => '<',
                                ),
                            );
                            $query_args = array(
                                'post_type' => LP_COURSE_CPT,
                                'posts_per_page' => 1,
                                'meta_query' => $meta_query,
                                'fields' => 'ids'
                            );
                            $loop = new WP_Query($query_args);
                            $courses_count = $loop->found_posts;
                            ?>
                            <li>
                                <input id="filter-rating-<?php echo esc_attr($i); ?>" type="radio" class="d-none" name="filter-rating" value="<?php echo esc_html($i); ?>" <?php checked($selected, $i); ?>>
                                <label for="filter-rating-<?php echo esc_attr($i); ?>">
                                    <?php 
                                    if ( class_exists( 'Educrat_Course_Review' ) ) {
                                        Educrat_Course_Review::print_review($i);
                                    } else {
                                        echo str_repeat('★', $i) . str_repeat('☆', 5-$i);
                                    }
                                    ?>
                                    <span class="count">(<?php echo esc_html($courses_count); ?>)</span>
                                </label>
                            </li>
                        <?php } ?>
                    </ul>
                <?php } ?>

                <input type="hidden" name="post_type" value="<?php echo esc_attr( LP_COURSE_CPT ); ?>">
                <input type="hidden" name="taxonomy" value="<?php echo esc_attr( get_queried_object()->taxonomy ?? $_GET['taxonomy'] ?? '' ); ?>">
                <input type="hidden" name="term_id" value="<?php echo esc_attr( get_queried_object()->term_id ?? $_GET['term_id'] ?? '' ); ?>">
                <input type="hidden" name="term" value="<?php echo esc_attr( get_queried_object()->slug ?? $_GET['term'] ?? '' ); ?>">
                
                <?php flavio_theme_query_string_form_fields( null, array( 'submit', 'paged', 'filter-rating' ) ); ?>
            </form>
        </div>
        <?php 
        echo trim($after_widget);
    }
}

/**
 * Course Filter Price Widget
 */
class Flavio_Theme_Widget_Course_Filter_Price extends WP_Widget {

    public function __construct() {
        parent::__construct(
            'flavio_theme_course_filter_price',
            esc_html__('LearnPress Courses:: Filter Prices', 'flavio-theme'),
            array( 'description' => esc_html__( 'Show list of course filter price', 'flavio-theme' ), )
        );
        $this->widgetName = 'courses_filter_price';
    }

    public function widget( $args, $instance ) {
        $this->render_price_widget( $args, $instance );
    }

    public function form( $instance ) {
        $defaults = array(
            'title' => 'Prices',
            'layout' => 'list',
        );
        $instance = wp_parse_args( (array) $instance, $defaults );

        $options = array(
            'select' => esc_html__('Select', 'flavio-theme'),
            'list' => esc_html__('List', 'flavio-theme')
        );
        ?>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id( 'title' )); ?>"><?php esc_html_e( 'Title:', 'flavio-theme' ); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id( 'title' )); ?>" name="<?php echo esc_attr($this->get_field_name( 'title' )); ?>" type="text" value="<?php echo esc_attr( $instance['title'] ); ?>" />
        </p>

        <p>
            <label for="<?php echo esc_attr($this->get_field_id('layout')); ?>">
                <?php echo esc_html__('Layout:', 'flavio-theme' ); ?>
            </label>
            <br>
            <select id="<?php echo esc_attr($this->get_field_id('layout')); ?>" name="<?php echo esc_attr($this->get_field_name('layout')); ?>">
                <?php foreach ($options as $key => $value) { ?>
                    <option value="<?php echo esc_attr( $key ); ?>" <?php selected($instance['layout'],$key); ?> ><?php echo esc_html( $value ); ?></option>
                <?php } ?>
            </select>
        </p>
        <?php
    }

    public function update( $new_instance, $old_instance ) {
        $instance = array();
        $instance['title'] = ( ! empty( $new_instance['title'] ) ) ? strip_tags( $new_instance['title'] ) : '';
        $instance['layout'] = ( ! empty( $new_instance['layout'] ) ) ? strip_tags( $new_instance['layout'] ) : '';
        return $instance;
    }

    private function render_price_widget( $args, $instance ) {
        if ( ! defined( 'LP_COURSE_CPT' ) ) {
            return;
        }

        extract( $args );
        extract( $instance );

        echo trim($before_widget);
        $title = apply_filters('widget_title', $instance['title']);

        if ( $title ) {
            echo trim($before_title) . trim( $title ) . $after_title;
        }

        $courses_page_id = learn_press_get_page_id( 'courses' );
        $url = get_permalink($courses_page_id);
        $selected = isset($_GET['filter-price']) ? $_GET['filter-price'] : '';
        ?>
        <div class="filter-price-widget">
            <form action="<?php echo esc_url($url); ?>" method="get">
                <?php if ( $layout == 'select' ) { ?>
                    <select name="filter-price" class="filter-price">
                        <?php
                        $query_args = array(
                            'post_type' => LP_COURSE_CPT,
                            'posts_per_page' => 1,
                            'fields' => 'ids'
                        );
                        $loop = new WP_Query($query_args);
                        $courses_count = $loop->found_posts;
                        ?>
                        <option value="" <?php selected($selected, ''); ?>><?php esc_html_e('All', 'flavio-theme'); ?> (<?php echo esc_html($courses_count); ?>)</option>

                        <?php
                        $meta_query = array(array(
                            'relation' => 'OR',
                            array(
                                'key' => '_lp_price',
                                'value' => '0',
                                'compare' => '=',
                            ),
                            array(
                                'key' => '_lp_price',
                                'value' => '',
                                'compare' => '=',
                            ),
                            array(
                                'key' => '_lp_price',
                                'compare' => 'NOT EXISTS',
                            )
                        ));
                        $query_args = array(
                            'post_type' => LP_COURSE_CPT,
                            'posts_per_page' => 1,
                            'meta_query' => $meta_query,
                            'fields' => 'ids'
                        );
                        $loop = new WP_Query($query_args);
                        $courses_count = $loop->found_posts;
                        ?>
                        <option value="free" <?php selected($selected, 'free'); ?>><?php esc_html_e('Free', 'flavio-theme'); ?> (<?php echo esc_html($courses_count); ?>)</option>

                        <?php
                        $meta_query = array( array(
                            'key' => '_lp_price',
                            'value' => '0',
                            'compare' => '>',
                        ));
                        $query_args = array(
                            'post_type' => LP_COURSE_CPT,
                            'posts_per_page' => 1,
                            'meta_query' => $meta_query,
                            'fields' => 'ids'
                        );
                        $loop = new WP_Query($query_args);
                        $courses_count = $loop->found_posts;
                        ?>
                        <option value="paid" <?php selected($selected, 'paid'); ?>><?php esc_html_e('Paid', 'flavio-theme'); ?> (<?php echo esc_html($courses_count); ?>)</option>
                    </select>
                <?php } else { ?>
                    <ul class="price-list course-list-check">
                        <li>
                            <?php
                            $query_args = array(
                                'post_type' => LP_COURSE_CPT,
                                'posts_per_page' => 1,
                                'fields' => 'ids'
                            );
                            $loop = new WP_Query($query_args);
                            $courses_count = $loop->found_posts;
                            ?>
                            <input id="filter-price-all" type="radio" class="d-none" name="filter-price" value="" <?php checked($selected, ''); ?>>
                            <label for="filter-price-all"><?php esc_html_e('All', 'flavio-theme'); ?> <span class="count">(<?php echo esc_html($courses_count); ?>)</span></label>
                        </li>
                        <li>
                            <?php
                            $meta_query = array(array(
                                'relation' => 'OR',
                                array(
                                    'key' => '_lp_price',
                                    'value' => '0',
                                    'compare' => '=',
                                ),
                                array(
                                    'key' => '_lp_price',
                                    'value' => '',
                                    'compare' => '=',
                                ),
                                array(
                                    'key' => '_lp_price',
                                    'compare' => 'NOT EXISTS',
                                )
                            ));
                            $query_args = array(
                                'post_type' => LP_COURSE_CPT,
                                'posts_per_page' => 1,
                                'meta_query' => $meta_query,
                                'fields' => 'ids'
                            );
                            $loop = new WP_Query($query_args);
                            $courses_count = $loop->found_posts;
                            ?>
                            <input id="filter-price-free" type="radio" class="d-none" name="filter-price" value="free" <?php checked($selected, 'free'); ?>>
                            <label for="filter-price-free"><?php esc_html_e('Free', 'flavio-theme'); ?> <span class="count">(<?php echo esc_html($courses_count); ?>)</span></label>
                        </li>
                        <li>
                            <?php
                            $meta_query = array( array(
                                'key' => '_lp_price',
                                'value' => '0',
                                'compare' => '>',
                            ));
                            $query_args = array(
                                'post_type' => LP_COURSE_CPT,
                                'posts_per_page' => 1,
                                'meta_query' => $meta_query,
                                'fields' => 'ids'
                            );
                            $loop = new WP_Query($query_args);
                            $courses_count = $loop->found_posts;
                            ?>
                            <input id="filter-price-paid" type="radio" class="d-none" name="filter-price" value="paid" <?php checked($selected, 'paid'); ?>>
                            <label for="filter-price-paid"><?php esc_html_e('Paid', 'flavio-theme'); ?> <span class="count">(<?php echo esc_html($courses_count); ?>)</span></label>
                        </li>
                    </ul>
                <?php } ?>
                <input type="hidden" name="post_type" value="<?php echo esc_attr( LP_COURSE_CPT ); ?>">
                <input type="hidden" name="taxonomy" value="<?php echo esc_attr( get_queried_object()->taxonomy ?? $_GET['taxonomy'] ?? '' ); ?>">
                <input type="hidden" name="term_id" value="<?php echo esc_attr( get_queried_object()->term_id ?? $_GET['term_id'] ?? '' ); ?>">
                <input type="hidden" name="term" value="<?php echo esc_attr( get_queried_object()->slug ?? $_GET['term'] ?? '' ); ?>">

                <?php flavio_theme_query_string_form_fields( null, array( 'submit', 'paged', 'filter-price' ) ); ?>
            </form>
        </div>
        <?php
        echo trim($after_widget);
    }
}

/**
 * Course Filter Level Widget
 */
class Flavio_Theme_Widget_Course_Filter_Level extends WP_Widget {

    public function __construct() {
        parent::__construct(
            'flavio_theme_course_filter_level',
            esc_html__('LearnPress Courses:: Filter Levels', 'flavio-theme'),
            array( 'description' => esc_html__( 'Show list of course filter level', 'flavio-theme' ), )
        );
        $this->widgetName = 'course_filter_level';
    }

    public function widget( $args, $instance ) {
        $this->render_level_widget( $args, $instance );
    }

    public function form( $instance ) {
        $defaults = array(
            'title' => 'Levels',
            'layout' => 'list',
        );
        $instance = wp_parse_args( (array) $instance, $defaults );

        $options = array(
            'select' => esc_html__('Select', 'flavio-theme'),
            'list' => esc_html__('List', 'flavio-theme')
        );
        ?>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id( 'title' )); ?>"><?php esc_html_e( 'Title:', 'flavio-theme' ); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id( 'title' )); ?>" name="<?php echo esc_attr($this->get_field_name( 'title' )); ?>" type="text" value="<?php echo esc_attr( $instance['title'] ); ?>" />
        </p>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('layout')); ?>">
                <?php echo esc_html__('Layout:', 'flavio-theme' ); ?>
            </label>
            <br>
            <select id="<?php echo esc_attr($this->get_field_id('layout')); ?>" name="<?php echo esc_attr($this->get_field_name('layout')); ?>">
                <?php foreach ($options as $key => $value) { ?>
                    <option value="<?php echo esc_attr( $key ); ?>" <?php selected($instance['layout'],$key); ?> ><?php echo esc_html( $value ); ?></option>
                <?php } ?>
            </select>
        </p>
        <?php
    }

    public function update( $new_instance, $old_instance ) {
        $instance = array();
        $instance['title'] = ( ! empty( $new_instance['title'] ) ) ? strip_tags( $new_instance['title'] ) : '';
        $instance['layout'] = ( ! empty( $new_instance['layout'] ) ) ? strip_tags( $new_instance['layout'] ) : '';
        return $instance;
    }

    private function render_level_widget( $args, $instance ) {
        if ( ! defined( 'LP_COURSE_CPT' ) ) {
            return;
        }

        $levels = flavio_theme_get_course_levels();

        if ( empty( $levels ) ) {
            return;
        }

        extract( $args );
        extract( $instance );

        echo trim($before_widget);
        $title = apply_filters('widget_title', $instance['title']);

        if ( $title ) {
            echo trim($before_title) . trim( $title ) . $after_title;
        }

        $courses_page_id = learn_press_get_page_id( 'courses' );
        $url = get_permalink($courses_page_id);
        $selected = isset($_GET['filter-level']) ? $_GET['filter-level'] : '';
        ?>
        <div class="filter-levels-widget">
            <form action="<?php echo esc_url($url); ?>" method="get">
                <?php if ( $layout == 'select' ) { ?>
                    <select name="filter-level" class="filter-level">
                        <?php foreach ($levels as $key => $title) {
                            if ( $key ) {
                                $meta_query = array(
                                    array(
                                        'key' => '_lp_level',
                                        'value' => $key,
                                        'compare' => '=',
                                    )
                                );
                            } else {
                                $meta_query = array();
                            }
                            $query_args = array(
                                'post_type' => LP_COURSE_CPT,
                                'posts_per_page' => 1,
                                'meta_query' => $meta_query,
                                'fields' => 'ids'
                            );
                            $loop = new WP_Query($query_args);
                            $courses_count = $loop->found_posts;
                        ?>
                            <option value="<?php echo esc_attr($key); ?>" <?php selected($selected, $key); ?>><?php echo esc_html($title); ?> (<?php echo esc_html($courses_count); ?>)</option>
                        <?php } ?>
                    </select>
                <?php } else { ?>
                    <ul class="level-list course-list-check">
                        <?php foreach ($levels as $key => $title) {
                            if ( $key ) {
                                $meta_query = array(
                                    array(
                                        'key' => '_lp_level',
                                        'value' => $key,
                                        'compare' => '=',
                                    )
                                );
                            } else {
                                $meta_query = array();
                            }
                            $query_args = array(
                                'post_type' => LP_COURSE_CPT,
                                'posts_per_page' => 1,
                                'meta_query' => $meta_query,
                                'fields' => 'ids'
                            );
                            $loop = new WP_Query($query_args);
                            $courses_count = $loop->found_posts;

                            $checked = '';
                            if ( !empty($selected) && is_array($selected) ) {
                                if ( in_array($key, $selected) ) {
                                    $checked = 'checked="checked"';
                                }
                            } elseif (!empty($selected)) {
                                $checked = checked($selected, $key, false);
                            }
                        ?>
                            <li>
                                <input id="filter-level-<?php echo esc_attr($key); ?>" type="checkbox" class="d-none" name="filter-level[]" value="<?php echo esc_attr($key); ?>" <?php echo trim($checked); ?>>
                                <label for="filter-level-<?php echo esc_attr($key); ?>"><?php echo esc_html($title); ?> <span class="count">(<?php echo esc_html($courses_count); ?>)</span></label>
                            </li>
                        <?php } ?>
                    </ul>
                <?php } ?>

                <input type="hidden" name="post_type" value="<?php echo esc_attr( LP_COURSE_CPT ); ?>">
                <input type="hidden" name="taxonomy" value="<?php echo esc_attr( get_queried_object()->taxonomy ?? $_GET['taxonomy'] ?? '' ); ?>">
                <input type="hidden" name="term_id" value="<?php echo esc_attr( get_queried_object()->term_id ?? $_GET['term_id'] ?? '' ); ?>">
                <input type="hidden" name="term" value="<?php echo esc_attr( get_queried_object()->slug ?? $_GET['term'] ?? '' ); ?>">

                <?php flavio_theme_query_string_form_fields( null, array( 'submit', 'paged', 'filter-level' ) ); ?>
            </form>
        </div>
        <?php
        echo trim($after_widget);
    }
}

/**
 * Course Filter Keywords Widget
 */
class Flavio_Theme_Widget_Course_Filter_Keywords extends WP_Widget {

    public function __construct() {
        parent::__construct(
            'flavio_theme_course_filter_keywords',
            esc_html__('LearnPress Courses:: Filter Keywords', 'flavio-theme'),
            array( 'description' => esc_html__( 'Show list of course filter keywords', 'flavio-theme' ), )
        );
        $this->widgetName = 'course_filter_keywords';
    }

    public function widget( $args, $instance ) {
        $this->render_keywords_widget( $args, $instance );
    }

    public function form( $instance ) {
        $defaults = array(
            'title' => '',
        );
        $instance = wp_parse_args( (array) $instance, $defaults );
        ?>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id( 'title' )); ?>"><?php esc_html_e( 'Title:', 'flavio-theme' ); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id( 'title' )); ?>" name="<?php echo esc_attr($this->get_field_name( 'title' )); ?>" type="text" value="<?php echo esc_attr( $instance['title'] ); ?>" />
        </p>
        <?php
    }

    public function update( $new_instance, $old_instance ) {
        $instance = array();
        $instance['title'] = ( ! empty( $new_instance['title'] ) ) ? strip_tags( $new_instance['title'] ) : '';
        return $instance;
    }

    private function render_keywords_widget( $args, $instance ) {
        if ( ! defined( 'LP_COURSE_CPT' ) ) {
            return;
        }

        extract( $args );
        extract( $instance );

        echo trim($before_widget);
        $title = apply_filters('widget_title', $instance['title']);

        if ( $title ) {
            echo trim($before_title) . trim( $title ) . $after_title;
        }

        $s = '';
        if ( class_exists( 'LP_Request' ) ) {
            $s = LP_Request::get( 'c_search' );
        } elseif ( isset( $_GET['c_search'] ) ) {
            $s = sanitize_text_field( $_GET['c_search'] );
        }
        ?>
        <div class="widget_search">
            <form class="search-courses" method="get" action="<?php echo esc_url( learn_press_get_page_link( 'courses' ) ); ?>">
                <input type="hidden" name="post_type" value="<?php echo esc_attr( LP_COURSE_CPT ); ?>">
                <input type="hidden" name="taxonomy" value="<?php echo esc_attr( get_queried_object()->taxonomy ?? $_GET['taxonomy'] ?? '' ); ?>">
                <input type="hidden" name="term_id" value="<?php echo esc_attr( get_queried_object()->term_id ?? $_GET['term_id'] ?? '' ); ?>">
                <input type="hidden" name="term" value="<?php echo esc_attr( get_queried_object()->slug ?? $_GET['term'] ?? '' ); ?>">
                <input type="text" class="form-control" placeholder="<?php esc_attr_e( 'Search courses...', 'flavio-theme' ); ?>" name="c_search" value="<?php echo esc_attr( $s ); ?>">
                <button class="btn" type="submit"><i class="flaticon-search"></i></button>

                <?php flavio_theme_query_string_form_fields( null, array( 'submit', 'paged', 'c_search' ) ); ?>
            </form>
        </div>
        <?php
        echo trim($after_widget);
    }
}
