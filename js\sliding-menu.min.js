/*! =========================================================
 * Sliding Menu v0.3.0
 * http://github.danielcardoso.net/sliding-menu/
 * ==========================================================
 * Copyright (c) 2014-2019 DanielCardoso.net.
 * Licensed under MIT.
 * ======================================================== */
if("undefined"==typeof jQuery)throw new Error("Sliding Menu requires jQuery");!function(t){"use strict";"function"==typeof define&&define.amd?define(["jquery"],t):t(jQuery)}(function(l){"use strict";var e,h;e=[],(h=function(t,e){this.options=void 0,this.$el=void 0,this.currentPanel=void 0,this.init(t,e)}).NAME="Sliding Menu",h.VERSION="0.3.0",h.PANEL_CLASS=(h.MAIN_CLASS="sliding-menu")+"__panel",h.ICON_CLASS=h.MAIN_CLASS+"__icon",h.NAVIGATION_CLASS=h.MAIN_CLASS+"__nav",h.BACK_CLASS=h.MAIN_CLASS+"__back",h.SEPARATOR_CLASS=h.MAIN_CLASS+"__separator",h.SET_ICON_CLASS="sm-set-icon",h.DEFAULTS={className:"",transitionDuration:250,dataJSON:!1,initHref:!1,backLabel:"Back"},h.prototype.init=function(t,e){this.$el=l(t),this.$el.hasClass(h.MAIN_CLASS)||(this.options=this.getOptions(e),this.events(),this.process())},h.prototype.$=function(t){return this.$el.find(t)},h.prototype.events=function(){this.$el.on("click","a, ."+h.NAVIGATION_CLASS,l.proxy(this._onClickItem,this))},h.prototype._onClickItem=function(t){var e,i,s;void 0!==(e=l(t.currentTarget)).attr("data-id")&&(s=e.hasClass(h.BACK_CLASS),i=this.$("."+h.PANEL_CLASS+'[data-id="'+e.attr("data-id")+'"]'),this.currentPanel.attr("data-id")!==i.attr("data-id")?(this.currentPanel.stop(!0,!0).animate({left:s?"100%":"-100%"},this.options.transitionDuration),i.stop(!0,!0).css("left",s?"-100%":"100%").animate({left:0},this.options.transitionDuration),this.$el.stop(!0,!0).animate({height:i.height()},this.options.transitionDuration)):(i.css({left:0}),this.$el.height(i.height())),this.currentPanel=i),e.hasClass(h.NAVIGATION_CLASS)||(this.$("li.active").removeClass("active"),e.closest("li").addClass("active"))},h.prototype.process=function(){var t;t=!0===this.options.dataJSON?this.processJSON(this.options.dataJSON):this.processHTML(),this.setMenuContent(t)},h.prototype.processJSON=function(t,e,i){var s,n;return s={id:h.PANEL_CLASS+"-"+this.getNewId(),root:!e,children:[]},n=[],e&&s.children.push({panelId:e.id,href:!1,label:!0===this.options.backLabel?i:this.options.backLabel,_styleClass:h.BACK_CLASS+" "+h.NAVIGATION_CLASS}),l(t).each(l.proxy(function(t,e){var i;s.children.push(e),e.children&&(i=this.processJSON(e.children,s,e.label),e.panelId=i[0].id,e._styleClass=h.NAVIGATION_CLASS,n=n.concat(i),delete e.children)},this)),[s].concat(n)},h.prototype.processHTML=function(t,e,i){var r,o;return r={id:h.PANEL_CLASS+"-"+this.getNewId(),root:!t,children:[]},o=[],void 0!==t?r.children.push({panelId:e.id,href:!1,label:!0===this.options.backLabel?i:this.options.backLabel,_styleClass:h.BACK_CLASS+" "+h.NAVIGATION_CLASS}):t=this.$el.children("ul"),t.children("li").each(l.proxy(function(t,e){var i,s,n,a;(e=l(e)).hasClass("separator")?i={separator:!0}:(void 0!==(i={icon:(s=e.children("a")).find("."+h.SET_ICON_CLASS).attr("class")||void 0,href:s.attr("href"),label:this.trimWhiteSpaces(s.text()),classNames:{parent:this.trimWhiteSpaces(e.attr("class")||""),element:this.trimWhiteSpaces(s.attr("class")||"")}}).icon&&(i.icon=i.icon.replace(h.SET_ICON_CLASS,"")),0!==(a=e.children("ul")).length&&(n=this.processHTML(a,r,i.label),i.panelId=n[0].id,i._styleClass=h.NAVIGATION_CLASS,o=o.concat(n))),r.children.push(i)},this)),[r].concat(o)},h.prototype.setMenuContent=function(t){var i;this.$el.empty().addClass(h.MAIN_CLASS+" "+this.options.className),l(t).each(l.proxy(function(t,e){var a;a=l("<ul/>"),e.root&&(i="."+h.PANEL_CLASS+'[data-id="'+e.id+'"]'),a.attr("data-id",e.id),a.addClass(h.PANEL_CLASS),l(e.children).each(function(t,e){var i,s,n;i=l("<li/>"),!0!==e.separator?(s=e.panelId?l("<button/>"):l("<a/>"),void 0!==e.classNames&&(i.addClass(e.classNames.parent),s.addClass(e.classNames.element)),s.addClass(e._styleClass),e.panelId?s.attr({type:"button","data-id":e.panelId}):s.attr({href:e.href}),s.text(e.label),e.icon&&((n=l("<i/>")).addClass(h.ICON_CLASS+" "+e.icon),s.prepend(n)),i.append(s)):i.addClass(h.SEPARATOR_CLASS),a.append(i)}),this.$el.append(a)},this)),(i=this.$(i)).addClass(h.PANEL_CLASS+"-root"),this.currentPanel=i,!1!==this.options.initHref?this.changeVisiblePanel():this.currentPanel.css("left",0),this.$el.height(this.currentPanel.height())},h.prototype.changeVisiblePanel=function(){var t,e;0!==(t=this.getHyperlinkByHref(this.options.initHref)).length?(t.closest("li").addClass("active"),e=this.getPanelByHref(this.options.initHref),this.currentPanel=e):console.warn(h.NAME+': the link "'+this.options.initHref+'" does not exists. Please check the '+(!1!==this.options.dataJSON?"JSON object":"HTML structure")+"."),this.options.initHref=!1,this.currentPanel.css("left",0)},h.prototype.trimWhiteSpaces=function(t){return t.trim()},h.prototype.getDefaults=function(){return h.DEFAULTS},h.prototype.getOptions=function(t){return l.extend({},this.getDefaults(),this.$el.data(),t)},h.prototype.getHyperlinkByHref=function(t){return this.$('a[href="'+t+'"]')||void 0},h.prototype.getPanelByHref=function(t){var e=this.getHyperlinkByHref(t);return void 0!==e?e.closest("ul"):void 0},h.prototype.getNewId=function(){for(var t;t=Math.random().toString(36).substring(2,9),0<=e.indexOf(t););return e.push(t),t};var t=l.fn.slidingMenu;l.fn.slidingMenu=function(s){return this.each(function(){var t,e,i;e=(t=l(this)).data("dc.slidingMenu"),i="object"==typeof s&&s,!e&&/destroy|hide/.test(s)||(e||t.data("dc.slidingMenu",e=new h(this,i)),"string"==typeof s&&e[s]())})},l.fn.slidingMenu.Constructor=h,l.fn.slidingMenu.noConflict=function(){return l.fn.slidingMenu=t,this}});