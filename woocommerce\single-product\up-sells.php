<?php
/**
 * Single Product Up-Sells
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/single-product/up-sells.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see         https://woocommerce.com/document/template-structure/
 * @package     WooCommerce\Templates
 * @version     9.6.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

$show_product_upsells = educrat_get_config('show_product_upsells', true);
if ( !$show_product_upsells ) {
    return;
}
$columns = educrat_get_config('upsells_product_columns', true);

global $product;

$upsells = $product->get_upsell_ids();

if ( sizeof( $upsells ) == 0 ) {
	return;
}

$args = array(
	'post_type'           => 'product',
	'ignore_sticky_posts' => 1,
	'no_found_rows'       => 1,
	'posts_per_page'      => -1,
	'post__in'            => $upsells,
);

$products = new WP_Query( $args );

if ( $products->have_posts() ) : ?>

	<div class="related products widget">
		<div class="woocommerce">
			<h2 class="widget-title"><?php esc_html_e( 'Up-Sells Products', 'educrat' ) ?></h2>
			<?php wc_get_template( 'layout-products/carousel.php',array( 'loop' => $products, 'columns'=> $columns, 'show_nav' => 1, 'slick_top' => 'slick-carousel-top' ) ); ?>
		</div>
	</div>
<?php endif;

wp_reset_postdata();