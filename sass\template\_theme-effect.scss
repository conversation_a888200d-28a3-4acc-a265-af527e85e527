// zoom
.effect-1{
    position:relative;
    &:after{
        content: '';
        display: block;
        @include size(0px,1px);
        @include transition(all 0.3s ease 0s);
        left:0;
        bottom:0;
        right:0;
        background:transparent;
        margin:auto;
    }
    &:hover{
        &:after{
            @include size(100%,1px);
            background:$theme-color;
        }
    }
}
.zoom-2{
    overflow: hidden;
    display: block;
    @include border-radius(3px);
    img{
        position: relative;
        width: percentage(1);
        height: auto;
        @include transition(all .2s ease-out);
    }
    &:hover{
        img{
            @include scale(1.2);
        }
    }
}

.close{ 
    .fa{
        @include transition(all 1s ease-in-out);
    }
    &:hover{
        .fa{
            @include rotate(360deg);
        }
    }
}

.image-overlay-1{
    &:after,&:before{
        content:"";
        display: block;
        position: absolute;
        z-index: 100;
        background: rgba($black,.7);
        @include size(percentage(1),percentage(1));
        left: 0;
        @include opacity(0);
        @include transition(all 0.3s ease 0s);
    }
    &:after{
        top: -100%;
    }
    &:before{
        bottom: -100%;
    }
    &:hover{
        &:after{
            top: -50%;
            @include opacity(1);
        }
        &:before{
            bottom: -50%;
            @include opacity(1);
        }
    }
}