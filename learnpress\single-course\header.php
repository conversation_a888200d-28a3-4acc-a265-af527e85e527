<?php

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}
global $post;
$course = learn_press_get_course();
$layout_type = educrat_course_layout_type();
?>

<style>
    .btn-add-course-to-cart-mobile{
    width: 100%;
    text-align: center;
    border-color: #ffb606;
    background: #ffb606;
    color: #fff !important;
    border: 1px solid transparent;
    padding: 0.65rem 1.875rem;
    height: auto;
    font-size: 0.9375rem;
    font-family: var(--bs-font-sans-serif);
    font-weight: 500;
    line-height: 1.8;
    border-radius: 8px;
    opacity: 1;
    }

.ratings i{
    
    color:#cecece;
    font-size:32px;
}

.rating-color{
    color:#fbc634 !important;
}

.review-count{
    font-weight:400;
    margin-bottom:2px;
    font-size:24px !important;
}

.small-ratings i{
  color:#cecece;   
}

.review-stat{
    font-weight:300;
    font-size:18px;
    margin-bottom:2px;
}



    </style>
<div class="course-header default <?php echo esc_attr($layout_type); ?>">
    <div class="container">
        <?php educrat_render_breadcrumbs_simple(); ?>
        <div class="inner-default">
            <div class="col-xl-8">
                <div class="course-header-left">
                    <div class="course-category">
                        <?php
                        $categories = get_the_terms( $post->ID, 'course_category' );
                        if ( $categories ) {
                            foreach ($categories as $term) {
                                ?>
                                <a class="course-category-item" href="<?php echo get_term_link($term); ?>"><?php echo esc_html($term->name); ?></a>
                                <?php
                            }
                        }
                        ?>
                    </div>
                    <h2 class="title"><?php the_title(); ?></h2>

                    <?php if(has_excerpt()){ ?>
                        <div class="excerpt">
                            <?php echo get_the_excerpt(); ?>
                        </div>
                    <?php } ?>

                    <div class="course-header-meta">
                        <!-- rating -->
                        <div class="rating">
                            <div class="wrapper_rating_avg d-flex align-items-center">
                                <?php
                                    $rating_avg = Educrat_Course_Review::get_ratings_average($post->ID);
                                    $total = Educrat_Course_Review::get_total_reviews( $post->ID );
                                    if($total > 0) {
                                ?>
                                    <span class="rating_avg"><?php echo number_format($rating_avg, 1,".","."); ?></span>
                                    <?php Educrat_Course_Review::print_review_single_course($rating_avg); ?>
                                <?php } ?>
                            </div>
                        </div>
                        <div class="course-student-number course-meta-field">
                            <i class="flaticon-online-course-1"></i>
                            <?php
                                $count = $course->count_students();
                                echo number_format($count);
                            ?>
                            <span><?php esc_html_e('Enrolled', 'educrat'); ?></span>
                        </div>
                        <!-- time -->
                        <?php
                        $duration = $course->get_data( 'duration' );
                        ?>
                        <div class="course-duration course-meta-field">
                            <i class="flaticon-clock"></i>
                            <?php echo trim( $duration ); ?>
                        </div>
                    </div>
                    <div class="course-header-bottom">
                        <div class="lp-course-author d-flex align-items-center">
                            <div class="course-author__pull-left d-flex align-items-center justify-content-center">
                                <?php echo trim($course->get_instructor()->get_profile_picture()); ?>
                            </div>
                            <div class="author-title"><?php echo trim($course->get_instructor_html()); ?></div>
							<div class="author-title"><a class="btn-add-course-to-cart-mobile mobile-only" href="<?=get_home_url() ?>/?add-to-cart=<?=$post->ID ?>">شراء الدورة</a></div>
                        </div>
                    </div>
                </div>

            </div>

        </div>
    </div>
</div>

<style>
    /* Hide the button by default */
    .mobile-only {
        display: none;
    }

    /* Show the button on screens with a maximum width of 767 pixels (mobile screens) */
    @media only screen and (max-width: 767px) {
        .mobile-only {
            display: block;
        }
    }
</style>