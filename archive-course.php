<?php
/**
 * Template for displaying content of archive courses page.
 *
 * <AUTHOR>
 * @package LearnPress/Templates
 * @version 4.0.0
 */

defined( 'ABSPATH' ) || exit;

get_header();

$sidebar_configs = educrat_get_course_archive_layout_configs();
$checkmain = educrat_get_config('sidebar-course_layout', '');
if($checkmain == 'main'){
    $checkmain = 'only_main';
}
educrat_render_breadcrumbs();

$display_mode = educrat_courses_get_display_mode();
$columns = educrat_get_config('courses_columns', 3);
$inner = $classes = $mobile_icon_sidebar = '';
if ( $display_mode == 'list' ) {
    $bcol = 12/$columns;
    $classes = 'col-lg-'.$bcol.' col-12';
    $inner = 'list';
} else {
    $bcol = 12/$columns;
    $classes = 'col-lg-'.$bcol.' col-md-6 col-12';
    $inner = educrat_get_config('courses_item_style', '');
}

if ( defined('EDUCRAT_DEMO_MODE') && EDUCRAT_DEMO_MODE ) {
    // Your demo mode code here
}

?>

<section id="main-container" class="main-content <?php echo apply_filters('educrat_course_content_class', 'container');?> inner <?php echo esc_attr($checkmain); ?>">
    <?php educrat_before_content( $sidebar_configs ); ?>
    <div class="row">
        <?php educrat_display_sidebar_left( $sidebar_configs ); ?>

        <div id="main-content" class="col-12 <?php echo esc_attr($sidebar_configs['main']['class']); ?>">
            <main id="main" class="site-main layout-courses display-mode-<?php echo esc_attr($display_mode); ?>" role="main">

                <?php
                /**
                 * LP Hook
                 */
                do_action( 'learn-press/before-courses-loop' );

                LP()->template( 'course' )->begin_courses_loop();

                // Display courses with specific IDs first
                $specific_course_ids = array(11298, 11296, 11289,20248, 34994 );
                $specific_courses_query = new WP_Query( array(
                    'post_type'      => 'lp_course',
                    'posts_per_page' => -1,
                    'post__in'       => $specific_course_ids,
                    'orderby'        => 'post__in'
                ) );

                while ( $specific_courses_query->have_posts() ) :
                    $specific_courses_query->the_post();
                    ?>
                        <?php learn_press_get_template_part( 'content-course', $inner ); ?>
                    <?php
                endwhile;
                wp_reset_postdata(); // Reset the post data

                // Display remaining courses
                while ( have_posts() ) :
                    the_post();
                    $post_id = get_the_ID();
                    if ( in_array( $post_id, $specific_course_ids ) ) {
                        continue; // Skip already displayed specific courses
                    }
                    ?>
                        <?php learn_press_get_template_part( 'content-course', $inner ); ?>
                    <?php
                endwhile;

                LP()->template( 'course' )->end_courses_loop();

                /**
                 * @since 3.0.0
                 */
                do_action( 'learn-press/after-courses-loop' );

                /**
                 * LP Hook
                 */
                // do_action( 'learn-press/after-main-content' );

                educrat_paging_nav();

                ?>

            </main><!-- .site-main -->
        </div><!-- .content-area -->

        <?php educrat_display_sidebar_right( $sidebar_configs ); ?>

    </div>
</section>

<?php

get_footer();

