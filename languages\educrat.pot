# Copyright (C) 2023 ApusTheme
# This file is distributed under the GNU General Public License v2 or later.
msgid ""
msgstr ""
"Project-Id-Version: Educrat 1.0.13\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/theme/educrat\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2023-05-19T09:36:11+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.5.0\n"
"X-Domain: educrat\n"

#. Theme Name of the theme
msgid "Educrat"
msgstr ""

#. Theme URI of the theme
msgid "https://themeforest.net/item/educrat-online-course-education-wordpress-theme/39691021"
msgstr ""

#. Description of the theme
msgid "Educrat is a listing directory WordPress theme that will help you create, manage and monetize a local or global directory site."
msgstr ""

#. Author of the theme
msgid "ApusTheme"
msgstr ""

#. Author URI of the theme
msgid "https://themeforest.net/user/apustheme"
msgstr ""

#. Template Name of the theme
#: inc/customizer/customizer.php:561
msgid "404 Page"
msgstr ""

#: 404.php:52
msgid "Page Not Found!"
msgstr ""

#: 404.php:62
msgid " The page you 're looking for isn't available. Try to search again or use the go to."
msgstr ""

#: 404.php:68
msgid "Go Back To Homepage"
msgstr ""

#: comments.php:26
#: woocommerce/single-product-reviews.php:30
msgid "0 Comments"
msgstr ""

#: comments.php:26
#: woocommerce/single-product-reviews.php:30
msgid "1 Comment"
msgstr ""

#: comments.php:26
#: woocommerce/single-product-reviews.php:30
msgid "% Comments"
msgstr ""

#: comments.php:39
msgid "Comments are closed."
msgstr ""

#: comments.php:45
msgid "Leave a Comment"
msgstr ""

#: comments.php:47
msgid "Comment"
msgstr ""

#: comments.php:48
msgid "Enter Your Comment"
msgstr ""

#: comments.php:54
#: inc/vendors/elementor/widgets/testimonials.php:52
#: inc/vendors/simple-event/functions.php:157
#: learnpress/single-course/reviews.php:101
#: learnpress/single-course/reviews.php:135
#: woocommerce/single-product-reviews.php:81
msgid "Name"
msgstr ""

#: comments.php:55
#: learnpress/single-course/reviews.php:102
#: learnpress/single-course/reviews.php:136
#: woocommerce/single-product-reviews.php:82
msgid "Your Name"
msgstr ""

#: comments.php:58
#: learnpress/single-course/reviews.php:104
#: learnpress/single-course/reviews.php:138
#: woocommerce/single-product-reviews.php:85
msgid "Email"
msgstr ""

#: comments.php:59
#: woocommerce/single-product-reviews.php:86
msgid "Your Email"
msgstr ""

#: comments.php:62
#: learnpress/single-course/reviews.php:106
#: learnpress/single-course/reviews.php:140
msgid "Website"
msgstr ""

#: comments.php:66
msgid "Submit Comment"
msgstr ""

#: content-search.php:29
#: learnpress/content-course-list-v3.php:109
#: learnpress/content-course-list-v4.php:113
#: template-posts/loop/inner-list.php:29
#: templates-event/loop/inner-v2.php:20
#: tutor/loop/course/course-list-v3.php:119
#: tutor/loop/course/course-list-v4.php:132
msgid "Read More"
msgstr ""

#: footer.php:28
msgid "&copy; %s - Educrat. All Rights Reserved. <br/> Powered by <a href=\"//themeforest.net/user/apustheme\">ApusTheme</a>"
msgstr ""

#: functions.php:72
msgid "Primary Menu"
msgstr ""

#: functions.php:73
msgid "Primary Mobile Menu"
msgstr ""

#: functions.php:74
msgid "Vertical Menu"
msgstr ""

#: functions.php:75
msgid "User Account Menu"
msgstr ""

#: functions.php:285
msgid "Days"
msgstr ""

#: functions.php:286
msgid "Hrs"
msgstr ""

#: functions.php:287
msgid "Mins"
msgstr ""

#: functions.php:288
msgid "Secs"
msgstr ""

#: functions.php:311
msgid "Previous"
msgstr ""

#: functions.php:312
#: template-posts/single/inner.php:68
msgid "Next"
msgstr ""

#: functions.php:313
msgid "Back"
msgstr ""

#: functions.php:367
msgid "Sidebar Default"
msgstr ""

#: functions.php:369
#: functions.php:379
#: functions.php:389
msgid "Add widgets here to appear in your Sidebar."
msgstr ""

#: functions.php:377
msgid "Sidebar Home 9"
msgstr ""

#: functions.php:387
msgid "Sidebar Home 10"
msgstr ""

#: functions.php:397
msgid "Courses sidebar"
msgstr ""

#: functions.php:399
#: functions.php:409
#: functions.php:419
#: functions.php:429
#: functions.php:439
#: functions.php:449
#: functions.php:459
msgid "Add widgets here to appear in your sidebar."
msgstr ""

#: functions.php:407
msgid "Courses top sidebar"
msgstr ""

#: functions.php:417
msgid "Course single sidebar"
msgstr ""

#: functions.php:427
msgid "Event sidebar"
msgstr ""

#: functions.php:437
msgid "Event Single sidebar"
msgstr ""

#: functions.php:447
msgid "Blog sidebar"
msgstr ""

#: functions.php:457
msgid "Shop sidebar"
msgstr ""

#: functions.php:467
msgid "Header Mobile Bottom"
msgstr ""

#: functions.php:469
msgid "Add widgets here to appear in your header mobile."
msgstr ""

#: functions.php:480
msgid "Apus Framework For Themes"
msgstr ""

#: functions.php:487
msgid "Elementor Page Builder"
msgstr ""

#: functions.php:493
msgid "Apus Simple Event"
msgstr ""

#: functions.php:500
#: inc/vendors/elementor/widgets/revslider.php:45
msgid "Revolution Slider"
msgstr ""

#: functions.php:507
msgid "Cmb2"
msgstr ""

#: functions.php:513
msgid "MailChimp for WordPress"
msgstr ""

#: functions.php:519
msgid "Contact Form 7"
msgstr ""

#: functions.php:526
msgid "Woocommerce"
msgstr ""

#: functions.php:532
msgid "One Click Demo Import"
msgstr ""

#: functions.php:538
msgid "SVG Support"
msgstr ""

#: headers/mobile/offcanvas-menu.php:25
#: headers/mobile/offcanvas-menu.php:47
msgid "Log in / Sign Up"
msgstr ""

#: inc/classes/megamenu.php:116
#: inc/classes/mobilemenu.php:106
#: inc/classes/mobileverticalmenu.php:106
msgid "New"
msgstr ""

#: inc/classes/megamenu.php:120
#: inc/classes/mobilemenu.php:110
#: inc/classes/mobileverticalmenu.php:110
msgid "Hot"
msgstr ""

#: inc/classes/megamenu.php:124
#: inc/classes/mobilemenu.php:114
#: inc/classes/mobileverticalmenu.php:114
msgid "Featured"
msgstr ""

#: inc/classes/userinfo.php:31
msgid "Wrong username or password. Please try again!!!"
msgstr ""

#: inc/classes/userinfo.php:34
msgid "Signin successful, redirecting..."
msgstr ""

#: inc/classes/userinfo.php:51
msgid "Enter an username or e-mail address."
msgstr ""

#: inc/classes/userinfo.php:57
msgid "There is no user registered with that email address."
msgstr ""

#: inc/classes/userinfo.php:63
msgid "There is no user registered with that username."
msgstr ""

#: inc/classes/userinfo.php:66
msgid "Invalid username or e-mail address."
msgstr ""

#: inc/classes/userinfo.php:83
msgid "Your new password"
msgstr ""

#: inc/classes/userinfo.php:85
msgid "Your new password is: "
msgstr ""

#: inc/classes/userinfo.php:92
msgid "Check your email address for you new password."
msgstr ""

#: inc/classes/userinfo.php:94
msgid "System is unable to send you mail containg your new password."
msgstr ""

#: inc/classes/userinfo.php:97
msgid "Oops! Something went wrong while updating your account."
msgstr ""

#: inc/classes/userinfo.php:116
msgid "Required form field is missing"
msgstr ""

#: inc/classes/userinfo.php:120
msgid "Username too short. At least 4 characters is required"
msgstr ""

#: inc/classes/userinfo.php:124
msgid "That username already exists!"
msgstr ""

#: inc/classes/userinfo.php:128
msgid "The username you entered is not valid"
msgstr ""

#: inc/classes/userinfo.php:132
msgid "Password length must be greater than 5"
msgstr ""

#: inc/classes/userinfo.php:136
msgid "Password must be equal Confirm Password"
msgstr ""

#: inc/classes/userinfo.php:140
msgid "Email is not valid"
msgstr ""

#: inc/classes/userinfo.php:144
msgid "Email Already in use"
msgstr ""

#: inc/classes/userinfo.php:169
msgid "You have registered, redirecting ..."
msgstr ""

#: inc/classes/userinfo.php:176
msgid "Register user error!"
msgstr ""

#: inc/classes/userinfo.php:191
msgid "LearnPress Job Title"
msgstr ""

#: inc/classes/userinfo.php:213
msgid "User Profile"
msgstr ""

#: inc/classes/userinfo.php:225
msgid "Tutor Facebook Profile"
msgstr ""

#: inc/classes/userinfo.php:226
msgid "Tutor Twitter Profile"
msgstr ""

#: inc/classes/userinfo.php:227
msgid "Tutor Youtube Profile"
msgstr ""

#: inc/classes/userinfo.php:228
msgid "Tutor Linkedin Profile"
msgstr ""

#: inc/customizer/customizer.php:13
#: inc/customizer/customizer.php:202
#: inc/vendors/learnpress/customizer.php:30
#: inc/vendors/simple-event/customizer.php:30
#: inc/vendors/tutor/customizer.php:30
#: inc/vendors/woocommerce/customizer.php:30
msgid "General"
msgstr ""

#: inc/customizer/customizer.php:27
msgid "Preload Website"
msgstr ""

#: inc/customizer/customizer.php:41
msgid "Preload Icon"
msgstr ""

#: inc/customizer/customizer.php:56
msgid "Image Lazy Loading"
msgstr ""

#: inc/customizer/customizer.php:64
msgid "Header"
msgstr ""

#: inc/customizer/customizer.php:75
#: inc/vendors/cmb2/page.php:76
msgid "Header Layout Type"
msgstr ""

#: inc/customizer/customizer.php:80
msgid "You can add or edit a header in <a href=\"%s\" target=\"_blank\">Headers Builder</a>"
msgstr ""

#: inc/customizer/customizer.php:92
msgid "Sticky Header"
msgstr ""

#: inc/customizer/customizer.php:106
msgid "Mobile Logo Upload"
msgstr ""

#: inc/customizer/customizer.php:121
msgid "Enable Header Mobile Menu"
msgstr ""

#: inc/customizer/customizer.php:136
msgid "Enable Header Mobile Login"
msgstr ""

#: inc/customizer/customizer.php:151
msgid "Enable Header Mobile \"Add Listing\" button"
msgstr ""

#: inc/customizer/customizer.php:159
msgid "Footer"
msgstr ""

#: inc/customizer/customizer.php:170
#: inc/vendors/cmb2/page.php:106
msgid "Footer Layout Type"
msgstr ""

#: inc/customizer/customizer.php:175
msgid "You can add or edit a header in <a href=\"%s\" target=\"_blank\">Footers Builder</a>"
msgstr ""

#: inc/customizer/customizer.php:188
msgid "Back To Top Button"
msgstr ""

#: inc/customizer/customizer.php:196
#: inc/functions-frontend.php:32
#: inc/functions-frontend.php:179
msgid "Blog"
msgstr ""

#: inc/customizer/customizer.php:217
#: inc/vendors/learnpress/customizer.php:45
#: inc/vendors/simple-event/customizer.php:45
#: inc/vendors/tutor/customizer.php:45
#: inc/vendors/woocommerce/customizer.php:45
msgid "Breadcrumbs"
msgstr ""

#: inc/customizer/customizer.php:232
#: inc/vendors/learnpress/customizer.php:60
#: inc/vendors/simple-event/customizer.php:60
#: inc/vendors/tutor/customizer.php:60
#: inc/vendors/woocommerce/customizer.php:60
msgid "Breadcrumbs Background Color"
msgstr ""

#: inc/customizer/customizer.php:246
#: inc/vendors/learnpress/customizer.php:75
#: inc/vendors/simple-event/customizer.php:75
#: inc/vendors/tutor/customizer.php:75
#: inc/vendors/woocommerce/customizer.php:75
msgid "Breadcrumbs Background"
msgstr ""

#: inc/customizer/customizer.php:254
msgid "Blog & Post Archives"
msgstr ""

#: inc/customizer/customizer.php:270
#: inc/customizer/customizer.php:433
#: inc/vendors/elementor/learnpress_widgets/search_form.php:85
#: inc/vendors/elementor/tutor_widgets/search_form.php:85
#: inc/vendors/learnpress/customizer.php:126
#: inc/vendors/learnpress/functions.php:403
#: inc/vendors/simple-event/customizer.php:138
#: inc/vendors/simple-event/customizer.php:304
#: inc/vendors/tutor/customizer.php:126
#: inc/vendors/tutor/functions.php:136
#: inc/vendors/woocommerce/customizer.php:199
#: inc/vendors/woocommerce/customizer.php:371
msgid "Layout Type"
msgstr ""

#: inc/customizer/customizer.php:275
#: inc/customizer/customizer.php:438
#: inc/vendors/learnpress/customizer.php:131
#: inc/vendors/simple-event/customizer.php:143
#: inc/vendors/simple-event/customizer.php:309
#: inc/vendors/tutor/customizer.php:131
#: inc/vendors/woocommerce/customizer.php:204
#: inc/vendors/woocommerce/customizer.php:375
msgid "Main Only"
msgstr ""

#: inc/customizer/customizer.php:279
#: inc/customizer/customizer.php:442
#: inc/vendors/learnpress/customizer.php:135
#: inc/vendors/simple-event/customizer.php:147
#: inc/vendors/simple-event/customizer.php:313
#: inc/vendors/tutor/customizer.php:135
#: inc/vendors/woocommerce/customizer.php:208
#: inc/vendors/woocommerce/customizer.php:376
msgid "Left - Main Sidebar"
msgstr ""

#: inc/customizer/customizer.php:283
#: inc/customizer/customizer.php:446
#: inc/vendors/learnpress/customizer.php:139
#: inc/vendors/simple-event/customizer.php:151
#: inc/vendors/simple-event/customizer.php:317
#: inc/vendors/tutor/customizer.php:139
#: inc/vendors/woocommerce/customizer.php:212
#: inc/vendors/woocommerce/customizer.php:377
msgid "Main - Right Sidebar"
msgstr ""

#: inc/customizer/customizer.php:288
#: inc/vendors/woocommerce/customizer.php:380
msgid "Select the variation you want to apply on your blog."
msgstr ""

#: inc/customizer/customizer.php:301
#: inc/customizer/customizer.php:464
#: inc/vendors/learnpress/customizer.php:110
#: inc/vendors/simple-event/customizer.php:122
#: inc/vendors/simple-event/customizer.php:288
#: inc/vendors/tutor/customizer.php:110
#: inc/vendors/woocommerce/customizer.php:230
#: inc/vendors/woocommerce/customizer.php:392
msgid "Is Full Width"
msgstr ""

#: inc/customizer/customizer.php:316
msgid "Show Top Categories"
msgstr ""

#: inc/customizer/customizer.php:329
#: inc/vendors/learnpress/customizer.php:12
#: inc/vendors/simple-event/customizer.php:12
#: inc/vendors/tutor/customizer.php:12
#: inc/vendors/woocommerce/customizer.php:12
msgid "1 Column"
msgstr ""

#: inc/customizer/customizer.php:330
#: inc/vendors/learnpress/customizer.php:13
#: inc/vendors/simple-event/customizer.php:13
#: inc/vendors/tutor/customizer.php:13
#: inc/vendors/woocommerce/customizer.php:13
msgid "2 Columns"
msgstr ""

#: inc/customizer/customizer.php:331
#: inc/vendors/learnpress/customizer.php:14
#: inc/vendors/simple-event/customizer.php:14
#: inc/vendors/tutor/customizer.php:14
#: inc/vendors/woocommerce/customizer.php:14
msgid "3 Columns"
msgstr ""

#: inc/customizer/customizer.php:332
#: inc/vendors/learnpress/customizer.php:15
#: inc/vendors/simple-event/customizer.php:15
#: inc/vendors/tutor/customizer.php:15
#: inc/vendors/woocommerce/customizer.php:15
msgid "4 Columns"
msgstr ""

#: inc/customizer/customizer.php:333
#: inc/vendors/learnpress/customizer.php:16
#: inc/vendors/simple-event/customizer.php:16
#: inc/vendors/tutor/customizer.php:16
#: inc/vendors/woocommerce/customizer.php:16
msgid "5 Columns"
msgstr ""

#: inc/customizer/customizer.php:334
#: inc/vendors/learnpress/customizer.php:17
#: inc/vendors/simple-event/customizer.php:17
#: inc/vendors/tutor/customizer.php:17
#: inc/vendors/woocommerce/customizer.php:17
msgid "6 Columns"
msgstr ""

#: inc/customizer/customizer.php:335
#: inc/vendors/learnpress/customizer.php:18
#: inc/vendors/simple-event/customizer.php:18
#: inc/vendors/tutor/customizer.php:18
#: inc/vendors/woocommerce/customizer.php:18
msgid "7 Columns"
msgstr ""

#: inc/customizer/customizer.php:336
#: inc/vendors/learnpress/customizer.php:19
#: inc/vendors/simple-event/customizer.php:19
#: inc/vendors/tutor/customizer.php:19
#: inc/vendors/woocommerce/customizer.php:19
msgid "8 Columns"
msgstr ""

#: inc/customizer/customizer.php:346
#: inc/customizer/customizer.php:476
#: inc/vendors/learnpress/customizer.php:155
#: inc/vendors/simple-event/customizer.php:167
#: inc/vendors/simple-event/customizer.php:333
#: inc/vendors/woocommerce/customizer.php:244
msgid "Archive Left Sidebar"
msgstr ""

#: inc/customizer/customizer.php:351
#: inc/customizer/customizer.php:481
#: inc/vendors/learnpress/customizer.php:160
#: inc/vendors/simple-event/customizer.php:172
#: inc/vendors/simple-event/customizer.php:338
#: inc/vendors/woocommerce/customizer.php:249
#: inc/vendors/woocommerce/customizer.php:409
msgid "Choose a sidebar for left sidebar"
msgstr ""

#: inc/customizer/customizer.php:361
#: inc/customizer/customizer.php:491
#: inc/vendors/learnpress/customizer.php:170
#: inc/vendors/simple-event/customizer.php:182
#: inc/vendors/simple-event/customizer.php:348
#: inc/vendors/woocommerce/customizer.php:259
msgid "Archive Right Sidebar"
msgstr ""

#: inc/customizer/customizer.php:366
#: inc/customizer/customizer.php:496
#: inc/vendors/learnpress/customizer.php:175
#: inc/vendors/simple-event/customizer.php:187
#: inc/vendors/simple-event/customizer.php:353
#: inc/vendors/woocommerce/customizer.php:264
#: inc/vendors/woocommerce/customizer.php:424
msgid "Choose a sidebar for right sidebar"
msgstr ""

#: inc/customizer/customizer.php:376
#: inc/vendors/woocommerce/customizer.php:123
msgid "Display Mode"
msgstr ""

#: inc/customizer/customizer.php:380
#: inc/vendors/elementor/event_widgets/events.php:81
#: inc/vendors/elementor/learnpress_widgets/courses.php:93
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:159
#: inc/vendors/elementor/learnpress_widgets/instructors.php:62
#: inc/vendors/elementor/tutor_widgets/courses.php:111
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:176
#: inc/vendors/elementor/tutor_widgets/instructors.php:62
#: inc/vendors/elementor/widgets/banners.php:80
#: inc/vendors/elementor/widgets/brands.php:90
#: inc/vendors/elementor/widgets/features_box.php:163
#: inc/vendors/elementor/widgets/posts.php:92
#: inc/vendors/elementor/widgets/posts.php:110
#: inc/vendors/elementor/woo_widgets/woo_products.php:88
#: inc/vendors/learnpress/customizer.php:207
#: inc/vendors/simple-event/customizer.php:201
#: inc/vendors/tutor/customizer.php:177
#: inc/vendors/woocommerce/customizer.php:127
msgid "Grid"
msgstr ""

#: inc/customizer/customizer.php:381
#: inc/vendors/elementor/widgets/posts.php:93
#: inc/vendors/elementor/widgets/posts.php:111
#: inc/vendors/learnpress/customizer.php:208
#: inc/vendors/simple-event/customizer.php:202
#: inc/vendors/simple-event/customizer.php:221
#: inc/vendors/tutor/customizer.php:178
#: inc/vendors/woocommerce/customizer.php:128
#: inc/widgets/filter-category.php:25
#: inc/widgets/filter-instructor.php:25
#: inc/widgets/filter-level.php:25
#: inc/widgets/filter-price.php:25
#: inc/widgets/filter-rating.php:25
msgid "List"
msgstr ""

#: inc/customizer/customizer.php:393
msgid "Blog Columns"
msgstr ""

#: inc/customizer/customizer.php:407
msgid "Thumbnail Size"
msgstr ""

#: inc/customizer/customizer.php:411
msgid "Enter thumbnail size. Example: thumbnail, medium, large, full or other sizes defined by current theme. Alternatively enter image size in pixels: 200x100 (Width x Height) ."
msgstr ""

#: inc/customizer/customizer.php:417
msgid "Blog Single"
msgstr ""

#: inc/customizer/customizer.php:451
msgid "Select the variation you want to apply on your blog single."
msgstr ""

#: inc/customizer/customizer.php:509
#: inc/vendors/learnpress/customizer.php:307
#: inc/vendors/simple-event/customizer.php:368
#: inc/vendors/tutor/customizer.php:277
#: inc/vendors/woocommerce/customizer.php:331
msgid "Show Social Share"
msgstr ""

#: inc/customizer/customizer.php:524
msgid "Show Related Posts"
msgstr ""

#: inc/customizer/customizer.php:538
msgid "Number related posts"
msgstr ""

#: inc/customizer/customizer.php:550
msgid "Related Blogs Columns"
msgstr ""

#: inc/customizer/customizer.php:574
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:71
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:69
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:71
#: inc/vendors/elementor/tutor_widgets/category_banner.php:78
#: inc/vendors/elementor/widgets/achievements.php:44
#: inc/vendors/elementor/widgets/address_box.php:46
#: inc/vendors/elementor/widgets/banner.php:37
#: inc/vendors/elementor/widgets/banners.php:140
#: inc/vendors/elementor/widgets/banners.php:164
#: inc/vendors/elementor/widgets/brands.php:214
#: inc/vendors/elementor/widgets/brands.php:258
#: inc/vendors/elementor/widgets/features_box.php:46
#: inc/vendors/elementor/widgets/list_icon.php:46
#: inc/vendors/elementor/widgets/team.php:155
#: inc/vendors/simple-event/functions.php:144
#: woocommerce/cart/cart.php:29
msgid "Image"
msgstr ""

#: inc/customizer/customizer.php:588
msgid "Top Image"
msgstr ""

#: inc/customizer/customizer.php:604
#: inc/vendors/elementor/header-widgets/primary_menu.php:48
#: inc/vendors/elementor/header-widgets/vertical_menu.php:36
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:73
#: inc/vendors/elementor/learnpress_widgets/my-wishlist.php:33
#: inc/vendors/elementor/tutor_widgets/courses.php:51
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:64
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:90
#: inc/vendors/elementor/tutor_widgets/my-wishlist.php:33
#: inc/vendors/elementor/widgets/achievements.php:82
#: inc/vendors/elementor/widgets/address_box.php:106
#: inc/vendors/elementor/widgets/banners.php:37
#: inc/vendors/elementor/widgets/banner_account.php:36
#: inc/vendors/elementor/widgets/brands.php:36
#: inc/vendors/elementor/widgets/brands.php:47
#: inc/vendors/elementor/widgets/call_to_action.php:36
#: inc/vendors/elementor/widgets/heading.php:101
#: inc/vendors/elementor/widgets/heading.php:108
#: inc/vendors/elementor/widgets/heading.php:224
#: inc/vendors/elementor/widgets/nav_menu.php:46
#: inc/vendors/elementor/widgets/nav_menu.php:142
#: inc/vendors/elementor/widgets/popup_video.php:40
#: inc/vendors/elementor/widgets/posts.php:34
#: inc/vendors/elementor/widgets/posts.php:62
#: inc/vendors/elementor/widgets/social_links.php:40
#: inc/vendors/elementor/widgets/social_links.php:207
#: inc/vendors/elementor/widgets/tabs.php:41
#: inc/vendors/elementor/widgets/tabs.php:122
#: inc/vendors/elementor/widgets/team.php:186
#: inc/vendors/elementor/widgets/testimonials.php:61
msgid "Title"
msgstr ""

#: inc/customizer/customizer.php:619
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:81
#: inc/vendors/elementor/tutor_widgets/category_banner.php:54
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:98
#: inc/vendors/elementor/widgets/banner_account.php:44
#: inc/vendors/elementor/widgets/call_to_action.php:45
#: inc/vendors/elementor/widgets/features_box.php:107
msgid "Description"
msgstr ""

#: inc/customizer/customizer.php:627
#: inc/vendors/elementor/widgets/banner.php:107
msgid "Theme Color"
msgstr ""

#: inc/customizer/customizer.php:640
msgid "Main Theme Color"
msgstr ""

#: inc/customizer/customizer.php:654
msgid "Main Theme Hover Color"
msgstr ""

#: inc/customizer/customizer.php:668
#: inc/vendors/elementor/widgets/heading.php:232
msgid "Text Color"
msgstr ""

#: inc/customizer/customizer.php:682
msgid "Link Color"
msgstr ""

#: inc/customizer/customizer.php:696
msgid "Link Hover Color"
msgstr ""

#: inc/customizer/customizer.php:710
#: inc/vendors/elementor/widgets/features_box.php:345
msgid "Heading Color"
msgstr ""

#: inc/customizer/customizer.php:724
msgid "Header Mobile Color"
msgstr ""

#: inc/customizer/customizer.php:733
#: inc/vendors/elementor/header-widgets/primary_menu.php:103
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:456
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:376
#: inc/vendors/elementor/learnpress_widgets/search_form.php:301
#: inc/vendors/elementor/learnpress_widgets/user_info.php:199
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:456
#: inc/vendors/elementor/tutor_widgets/category_banner.php:385
#: inc/vendors/elementor/tutor_widgets/search_form.php:301
#: inc/vendors/elementor/tutor_widgets/user_info.php:199
#: inc/vendors/elementor/widgets/features_box.php:468
#: inc/vendors/elementor/widgets/mailchimp.php:301
#: inc/vendors/elementor/widgets/social_links.php:319
msgid "Typography"
msgstr ""

#: inc/customizer/customizer.php:746
msgid "Main Font Face"
msgstr ""

#: inc/customizer/customizer.php:747
msgid "Pick the Main Font for your site"
msgstr ""

#: inc/customizer/customizer.php:763
msgid "Main Font Size"
msgstr ""

#: inc/customizer/customizer.php:766
msgid "Set a font size (px)"
msgstr ""

#: inc/customizer/customizer.php:778
msgid "Heading Font Face"
msgstr ""

#: inc/customizer/customizer.php:779
msgid "Pick the Heading Font for your site"
msgstr ""

#: inc/customizer/customizer.php:790
msgid "Social Media"
msgstr ""

#: inc/customizer/customizer.php:804
msgid "Enable Facebook Share"
msgstr ""

#: inc/customizer/customizer.php:819
msgid "Enable twitter Share"
msgstr ""

#: inc/customizer/customizer.php:834
msgid "Enable linkedin Share"
msgstr ""

#: inc/customizer/customizer.php:849
msgid "Enable pinterest Share"
msgstr ""

#: inc/customizer/customizer.php:904
#: inc/vendors/elementor/event_widgets/events.php:94
#: inc/vendors/elementor/header-widgets/vertical_menu.php:69
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:106
#: inc/vendors/elementor/learnpress_widgets/search_form.php:88
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:123
#: inc/vendors/elementor/tutor_widgets/search_form.php:88
#: inc/vendors/elementor/widgets/heading.php:150
#: inc/vendors/elementor/widgets/nav_menu.php:68
#: inc/vendors/elementor/widgets/posts.php:58
#: inc/vendors/elementor/widgets/posts.php:78
#: inc/vendors/elementor/widgets/tabs.php:144
#: inc/vendors/learnpress/functions.php:605
#: inc/vendors/simple-event/customizer.php:218
#: inc/vendors/simple-event/functions.php:240
#: inc/widgets/custom_menu.php:36
msgid "Default"
msgstr ""

#: inc/customizer/customizer.php:915
msgid "Dark"
msgstr ""

#: inc/customizer/customizer.php:926
msgid "Yellow"
msgstr ""

#: inc/customizer/customizer.php:937
msgid "Pink"
msgstr ""

#: inc/customizer/customizer.php:948
msgid "Purple"
msgstr ""

#: inc/customizer/customizer.php:959
msgid "Blue"
msgstr ""

#: inc/customizer/font/custom-controls.php:68
msgid "Choose a font"
msgstr ""

#: inc/customizer/font/custom-controls.php:76
msgid "Select weight & style for regular text"
msgstr ""

#: inc/customizer/font/custom-controls.php:90
msgid "Font Subsets"
msgstr ""

#: inc/functions-frontend.php:53
#: inc/functions-frontend.php:69
#: inc/functions-frontend.php:136
#: inc/functions-frontend.php:173
msgid "Error 404"
msgstr ""

#: inc/functions-frontend.php:61
#: inc/functions-frontend.php:165
msgid "Search results for \"%s\""
msgstr ""

#: inc/functions-frontend.php:63
#: inc/functions-frontend.php:167
msgid "Posts tagged \"%s\""
msgstr ""

#: inc/functions-frontend.php:82
#: inc/samples/sample-data.php:10
msgid "Home"
msgstr ""

#: inc/functions-frontend.php:171
msgid "Articles posted by "
msgstr ""

#: inc/functions-frontend.php:207
msgid "View all posts in %s"
msgstr ""

#: inc/functions-frontend.php:382
msgid "Posts navigation"
msgstr ""

#: inc/functions-frontend.php:434
msgid "%1$s"
msgstr ""

#: inc/functions-frontend.php:439
#: woocommerce/myaccount/my-address.php:47
msgid "Edit"
msgstr ""

#: inc/functions-frontend.php:440
#: learnpress/single-course/review.php:44
msgid "Reply"
msgstr ""

#: inc/functions-frontend.php:447
msgid "Your comment is awaiting moderation."
msgstr ""

#: inc/functions-frontend.php:515
#: inc/functions-frontend.php:528
#: tutor/archive-course-init.php:190
#: tutor/archive-course-init.php:217
msgid "Close"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:334
msgid "Install Required Plugins"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:335
msgid "Install Plugins"
msgstr ""

#. translators: %s: plugin name.
#: inc/plugins/class-tgm-plugin-activation.php:337
msgid "Installing Plugin: %s"
msgstr ""

#. translators: %s: plugin name.
#: inc/plugins/class-tgm-plugin-activation.php:339
msgid "Updating Plugin: %s"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:340
msgid "Something went wrong with the plugin API."
msgstr ""

#. translators: 1: plugin name(s).
#: inc/plugins/class-tgm-plugin-activation.php:341
msgid "This theme requires the following plugin: %1$s."
msgid_plural "This theme requires the following plugins: %1$s."
msgstr[0] ""
msgstr[1] ""

#. translators: 1: plugin name(s).
#: inc/plugins/class-tgm-plugin-activation.php:347
msgid "This theme recommends the following plugin: %1$s."
msgid_plural "This theme recommends the following plugins: %1$s."
msgstr[0] ""
msgstr[1] ""

#. translators: 1: plugin name(s).
#: inc/plugins/class-tgm-plugin-activation.php:353
msgid "The following plugin needs to be updated to its latest version to ensure maximum compatibility with this theme: %1$s."
msgid_plural "The following plugins need to be updated to their latest version to ensure maximum compatibility with this theme: %1$s."
msgstr[0] ""
msgstr[1] ""

#. translators: 1: plugin name(s).
#: inc/plugins/class-tgm-plugin-activation.php:359
msgid "There is an update available for: %1$s."
msgid_plural "There are updates available for the following plugins: %1$s."
msgstr[0] ""
msgstr[1] ""

#. translators: 1: plugin name(s).
#: inc/plugins/class-tgm-plugin-activation.php:365
msgid "The following required plugin is currently inactive: %1$s."
msgid_plural "The following required plugins are currently inactive: %1$s."
msgstr[0] ""
msgstr[1] ""

#. translators: 1: plugin name(s).
#: inc/plugins/class-tgm-plugin-activation.php:371
msgid "The following recommended plugin is currently inactive: %1$s."
msgid_plural "The following recommended plugins are currently inactive: %1$s."
msgstr[0] ""
msgstr[1] ""

#: inc/plugins/class-tgm-plugin-activation.php:377
msgid "Begin installing plugin"
msgid_plural "Begin installing plugins"
msgstr[0] ""
msgstr[1] ""

#: inc/plugins/class-tgm-plugin-activation.php:382
msgid "Begin updating plugin"
msgid_plural "Begin updating plugins"
msgstr[0] ""
msgstr[1] ""

#: inc/plugins/class-tgm-plugin-activation.php:387
msgid "Begin activating plugin"
msgid_plural "Begin activating plugins"
msgstr[0] ""
msgstr[1] ""

#: inc/plugins/class-tgm-plugin-activation.php:392
msgid "Return to Required Plugins Installer"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:393
#: inc/plugins/class-tgm-plugin-activation.php:917
#: inc/plugins/class-tgm-plugin-activation.php:2623
#: inc/plugins/class-tgm-plugin-activation.php:3670
msgid "Return to the Dashboard"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:394
#: inc/plugins/class-tgm-plugin-activation.php:3249
msgid "Plugin activated successfully."
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:395
#: inc/plugins/class-tgm-plugin-activation.php:3042
msgid "The following plugin was activated successfully:"
msgid_plural "The following plugins were activated successfully:"
msgstr[0] ""
msgstr[1] ""

#. translators: 1: plugin name.
#: inc/plugins/class-tgm-plugin-activation.php:397
msgid "No action taken. Plugin %1$s was already active."
msgstr ""

#. translators: 1: plugin name.
#: inc/plugins/class-tgm-plugin-activation.php:399
msgid "Plugin not activated. A higher version of %s is needed for this theme. Please update the plugin."
msgstr ""

#. translators: 1: dashboard link.
#: inc/plugins/class-tgm-plugin-activation.php:401
msgid "All plugins installed and activated successfully. %1$s"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:402
msgid "Dismiss this notice"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:403
msgid "There are one or more required or recommended plugins to install, update or activate."
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:404
msgid "Please contact the administrator of this site for help."
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:607
msgid "This plugin needs to be updated to be compatible with your theme."
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:608
msgid "Update Required"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:725
msgid "Set the parent_slug config variable instead."
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:1024
msgid "The remote plugin package does not contain a folder with the desired slug and renaming did not work."
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:1024
#: inc/plugins/class-tgm-plugin-activation.php:1027
msgid "Please contact the plugin provider and ask them to package their plugin according to the WordPress guidelines."
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:1027
msgid "The remote plugin package consists of more than one file, but the files are not packaged in a folder."
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:1211
#: inc/plugins/class-tgm-plugin-activation.php:3038
msgctxt "plugin A *and* plugin B"
msgid "and"
msgstr ""

#. translators: %s: version number
#: inc/plugins/class-tgm-plugin-activation.php:2072
msgid "TGMPA v%s"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2363
msgid "Required"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2366
msgid "Recommended"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2382
msgid "WordPress Repository"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2385
msgid "External Source"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2388
msgid "Pre-Packaged"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2405
msgid "Not Installed"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2409
msgid "Installed But Not Activated"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2411
msgid "Active"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2417
msgid "Required Update not Available"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2420
msgid "Requires Update"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2423
msgid "Update recommended"
msgstr ""

#. translators: 1: install status, 2: update status
#: inc/plugins/class-tgm-plugin-activation.php:2432
msgctxt "Install/Update Status"
msgid "%1$s, %2$s"
msgstr ""

#. translators: 1: number of plugins.
#: inc/plugins/class-tgm-plugin-activation.php:2478
msgctxt "plugins"
msgid "All <span class=\"count\">(%s)</span>"
msgid_plural "All <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#. translators: 1: number of plugins.
#: inc/plugins/class-tgm-plugin-activation.php:2482
msgid "To Install <span class=\"count\">(%s)</span>"
msgid_plural "To Install <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#. translators: 1: number of plugins.
#: inc/plugins/class-tgm-plugin-activation.php:2486
msgid "Update Available <span class=\"count\">(%s)</span>"
msgid_plural "Update Available <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#. translators: 1: number of plugins.
#: inc/plugins/class-tgm-plugin-activation.php:2490
msgid "To Activate <span class=\"count\">(%s)</span>"
msgid_plural "To Activate <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#: inc/plugins/class-tgm-plugin-activation.php:2572
msgctxt "as in: \"version nr unknown\""
msgid "unknown"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2580
msgid "Installed version:"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2588
msgid "Minimum required version:"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2600
msgid "Available version:"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2623
msgid "No plugins to install, update or activate."
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2637
msgid "Plugin"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2638
msgid "Source"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2639
msgid "Type"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2643
msgid "Version"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2644
#: woocommerce/myaccount/my-orders.php:36
#: woocommerce/myaccount/my-orders.php:57
msgid "Status"
msgstr ""

#. translators: %2$s: plugin name in screen reader markup
#: inc/plugins/class-tgm-plugin-activation.php:2693
msgid "Install %2$s"
msgstr ""

#. translators: %2$s: plugin name in screen reader markup
#: inc/plugins/class-tgm-plugin-activation.php:2698
msgid "Update %2$s"
msgstr ""

#. translators: %2$s: plugin name in screen reader markup
#: inc/plugins/class-tgm-plugin-activation.php:2704
msgid "Activate %2$s"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2774
msgid "Upgrade message from the plugin author:"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2807
msgid "Install"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2813
msgid "Update"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2816
msgid "Activate"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2847
msgid "No plugins were selected to be installed. No action taken."
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2849
msgid "No plugins were selected to be updated. No action taken."
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2890
msgid "No plugins are available to be installed at this time."
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2892
msgid "No plugins are available to be updated at this time."
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2998
msgid "No plugins were selected to be activated. No action taken."
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:3024
msgid "No plugins are available to be activated at this time."
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:3248
msgid "Plugin activation failed."
msgstr ""

#. translators: 1: plugin name, 2: action number 3: total number of actions.
#: inc/plugins/class-tgm-plugin-activation.php:3588
msgid "Updating Plugin %1$s (%2$d/%3$d)"
msgstr ""

#. translators: 1: plugin name, 2: error message.
#: inc/plugins/class-tgm-plugin-activation.php:3591
msgid "An error occurred while installing %1$s: <strong>%2$s</strong>."
msgstr ""

#. translators: 1: plugin name.
#: inc/plugins/class-tgm-plugin-activation.php:3593
msgid "The installation of %1$s failed."
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:3597
msgid "The installation and activation process is starting. This process may take a while on some hosts, so please be patient."
msgstr ""

#. translators: 1: plugin name.
#: inc/plugins/class-tgm-plugin-activation.php:3599
msgid "%1$s installed and activated successfully."
msgstr ""

#. translators: 1: plugin name.
#: inc/plugins/class-tgm-plugin-activation.php:3599
#: inc/plugins/class-tgm-plugin-activation.php:3607
msgid "Show Details"
msgstr ""

#. translators: 1: plugin name.
#: inc/plugins/class-tgm-plugin-activation.php:3599
#: inc/plugins/class-tgm-plugin-activation.php:3607
msgid "Hide Details"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:3600
msgid "All installations and activations have been completed."
msgstr ""

#. translators: 1: plugin name, 2: action number 3: total number of actions.
#: inc/plugins/class-tgm-plugin-activation.php:3602
msgid "Installing and Activating Plugin %1$s (%2$d/%3$d)"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:3605
msgid "The installation process is starting. This process may take a while on some hosts, so please be patient."
msgstr ""

#. translators: 1: plugin name.
#: inc/plugins/class-tgm-plugin-activation.php:3607
msgid "%1$s installed successfully."
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:3608
msgid "All installations have been completed."
msgstr ""

#. translators: 1: plugin name, 2: action number 3: total number of actions.
#: inc/plugins/class-tgm-plugin-activation.php:3610
msgid "Installing Plugin %1$s (%2$d/%3$d)"
msgstr ""

#: inc/template-tags.php:18
msgid "Comment navigation"
msgstr ""

#: inc/template-tags.php:21
msgid "Older Comments"
msgstr ""

#: inc/template-tags.php:25
msgid "Newer Comments"
msgstr ""

#: inc/vendors/cmb2/page.php:13
#: inc/vendors/cmb2/page.php:14
msgid "Global Setting"
msgstr ""

#: inc/vendors/cmb2/page.php:21
msgid "Select Layout"
msgstr ""

#: inc/vendors/cmb2/page.php:25
msgid "Main Content Only"
msgstr ""

#: inc/vendors/cmb2/page.php:26
msgid "Left Sidebar - Main Content"
msgstr ""

#: inc/vendors/cmb2/page.php:27
msgid "Main Content - Right Sidebar"
msgstr ""

#: inc/vendors/cmb2/page.php:33
msgid "Is Full Width?"
msgstr ""

#: inc/vendors/cmb2/page.php:36
#: inc/vendors/cmb2/page.php:57
#: inc/vendors/cmb2/page.php:87
#: inc/vendors/cmb2/page.php:98
#: inc/vendors/elementor/event_widgets/events.php:202
#: inc/vendors/elementor/event_widgets/events.php:217
#: inc/vendors/elementor/event_widgets/events.php:232
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:224
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:236
#: inc/vendors/elementor/learnpress_widgets/courses.php:195
#: inc/vendors/elementor/learnpress_widgets/courses.php:210
#: inc/vendors/elementor/learnpress_widgets/courses.php:225
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:245
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:260
#: inc/vendors/elementor/learnpress_widgets/instructors.php:166
#: inc/vendors/elementor/learnpress_widgets/instructors.php:181
#: inc/vendors/elementor/learnpress_widgets/instructors.php:196
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:224
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:236
#: inc/vendors/elementor/tutor_widgets/courses.php:213
#: inc/vendors/elementor/tutor_widgets/courses.php:228
#: inc/vendors/elementor/tutor_widgets/courses.php:243
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:262
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:277
#: inc/vendors/elementor/tutor_widgets/instructors.php:166
#: inc/vendors/elementor/tutor_widgets/instructors.php:181
#: inc/vendors/elementor/tutor_widgets/instructors.php:196
#: inc/vendors/elementor/widgets/testimonials.php:169
#: inc/vendors/elementor/woo_widgets/woo_products.php:175
#: inc/vendors/elementor/woo_widgets/woo_products.php:190
msgid "No"
msgstr ""

#: inc/vendors/cmb2/page.php:37
#: inc/vendors/cmb2/page.php:58
#: inc/vendors/cmb2/page.php:88
#: inc/vendors/cmb2/page.php:99
#: inc/vendors/elementor/event_widgets/events.php:201
#: inc/vendors/elementor/event_widgets/events.php:216
#: inc/vendors/elementor/event_widgets/events.php:231
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:223
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:235
#: inc/vendors/elementor/learnpress_widgets/courses.php:194
#: inc/vendors/elementor/learnpress_widgets/courses.php:209
#: inc/vendors/elementor/learnpress_widgets/courses.php:224
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:244
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:259
#: inc/vendors/elementor/learnpress_widgets/instructors.php:165
#: inc/vendors/elementor/learnpress_widgets/instructors.php:180
#: inc/vendors/elementor/learnpress_widgets/instructors.php:195
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:223
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:235
#: inc/vendors/elementor/tutor_widgets/courses.php:212
#: inc/vendors/elementor/tutor_widgets/courses.php:227
#: inc/vendors/elementor/tutor_widgets/courses.php:242
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:261
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:276
#: inc/vendors/elementor/tutor_widgets/instructors.php:165
#: inc/vendors/elementor/tutor_widgets/instructors.php:180
#: inc/vendors/elementor/tutor_widgets/instructors.php:195
#: inc/vendors/elementor/widgets/testimonials.php:168
#: inc/vendors/elementor/woo_widgets/woo_products.php:174
#: inc/vendors/elementor/woo_widgets/woo_products.php:189
msgid "Yes"
msgstr ""

#: inc/vendors/cmb2/page.php:43
msgid "Left Sidebar"
msgstr ""

#: inc/vendors/cmb2/page.php:49
msgid "Right Sidebar"
msgstr ""

#: inc/vendors/cmb2/page.php:55
msgid "Show Breadcrumb?"
msgstr ""

#: inc/vendors/cmb2/page.php:65
msgid "Breadcrumb Background Color"
msgstr ""

#: inc/vendors/cmb2/page.php:70
msgid "Breadcrumb Background Image"
msgstr ""

#: inc/vendors/cmb2/page.php:77
#: inc/vendors/cmb2/page.php:85
msgid "Choose a header for your website."
msgstr ""

#: inc/vendors/cmb2/page.php:84
msgid "Header Transparent"
msgstr ""

#: inc/vendors/cmb2/page.php:95
msgid "Header Fixed Top"
msgstr ""

#: inc/vendors/cmb2/page.php:96
msgid "Choose a header position"
msgstr ""

#: inc/vendors/cmb2/page.php:107
msgid "Choose a footer for your website."
msgstr ""

#: inc/vendors/cmb2/page.php:114
msgid "Extra Class"
msgstr ""

#: inc/vendors/cmb2/page.php:115
msgid "If you wish to style particular content element differently, then use this field to add a class name and then refer to it in your css file."
msgstr ""

#: inc/vendors/cmb2/page.php:121
msgid "Display Settings"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:15
msgid "Apus Events"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:27
msgid "Events"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:34
msgid "Get Events By"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:37
#: inc/widgets/events-list.php:26
msgid "Recent Events"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:38
#: inc/widgets/events-list.php:27
msgid "Upcoming Events"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:47
#: inc/vendors/elementor/learnpress_widgets/courses.php:48
#: inc/vendors/elementor/woo_widgets/woo_products.php:63
msgid "Categories Slug"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:51
#: inc/vendors/elementor/learnpress_widgets/courses.php:52
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:66
msgid "Enter id spearate by comma(,)"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:58
#: inc/vendors/elementor/learnpress_widgets/courses.php:59
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:126
#: inc/vendors/elementor/learnpress_widgets/instructors.php:35
#: inc/vendors/elementor/tutor_widgets/courses.php:77
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:143
#: inc/vendors/elementor/tutor_widgets/instructors.php:35
#: inc/vendors/elementor/woo_widgets/woo_products.php:74
msgid "Limit"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:61
#: inc/vendors/elementor/learnpress_widgets/courses.php:62
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:128
#: inc/vendors/elementor/learnpress_widgets/instructors.php:38
#: inc/vendors/elementor/tutor_widgets/courses.php:80
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:145
#: inc/vendors/elementor/tutor_widgets/instructors.php:38
#: inc/vendors/elementor/woo_widgets/woo_products.php:77
msgid "Enter number products to display"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:78
#: inc/vendors/elementor/header-widgets/vertical_menu.php:66
#: inc/vendors/elementor/learnpress_widgets/courses.php:90
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:156
#: inc/vendors/elementor/learnpress_widgets/instructors.php:59
#: inc/vendors/elementor/tutor_widgets/courses.php:108
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:173
#: inc/vendors/elementor/tutor_widgets/instructors.php:59
#: inc/vendors/elementor/widgets/banners.php:77
#: inc/vendors/elementor/widgets/brands.php:87
#: inc/vendors/elementor/widgets/features_box.php:159
#: inc/vendors/elementor/widgets/posts.php:106
#: inc/vendors/elementor/widgets/testimonials.php:151
#: inc/vendors/elementor/woo_widgets/woo_products.php:85
msgid "Layout"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:82
#: inc/vendors/elementor/learnpress_widgets/courses.php:94
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:160
#: inc/vendors/elementor/learnpress_widgets/instructors.php:63
#: inc/vendors/elementor/tutor_widgets/courses.php:112
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:177
#: inc/vendors/elementor/tutor_widgets/instructors.php:63
#: inc/vendors/elementor/widgets/banners.php:81
#: inc/vendors/elementor/widgets/brands.php:91
#: inc/vendors/elementor/widgets/features_box.php:162
#: inc/vendors/elementor/widgets/posts.php:109
#: inc/vendors/elementor/woo_widgets/woo_products.php:89
msgid "Carousel"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:91
msgid "Event Style"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:95
msgid "Grid v1"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:96
msgid "Grid v2"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:97
#: inc/vendors/elementor/learnpress_widgets/courses.php:77
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:143
#: inc/vendors/elementor/tutor_widgets/courses.php:95
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:160
#: inc/vendors/learnpress/customizer.php:228
#: inc/vendors/tutor/customizer.php:198
msgid "List 1"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:98
#: inc/vendors/elementor/learnpress_widgets/courses.php:78
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:144
#: inc/vendors/elementor/tutor_widgets/courses.php:96
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:161
#: inc/vendors/learnpress/customizer.php:229
#: inc/vendors/tutor/customizer.php:199
msgid "List 2"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:110
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:160
#: inc/vendors/elementor/learnpress_widgets/courses.php:106
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:172
#: inc/vendors/elementor/learnpress_widgets/instructors.php:75
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:160
#: inc/vendors/elementor/tutor_widgets/courses.php:124
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:189
#: inc/vendors/elementor/tutor_widgets/instructors.php:75
#: inc/vendors/elementor/widgets/address_box.php:146
#: inc/vendors/elementor/widgets/banners.php:90
#: inc/vendors/elementor/widgets/brands.php:103
#: inc/vendors/elementor/widgets/features_box.php:137
#: inc/vendors/elementor/widgets/posts.php:125
#: inc/vendors/elementor/widgets/testimonials.php:118
#: inc/vendors/elementor/woo_widgets/woo_products.php:101
msgid "Columns"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:121
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:171
#: inc/vendors/elementor/learnpress_widgets/courses.php:117
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:183
#: inc/vendors/elementor/learnpress_widgets/instructors.php:86
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:171
#: inc/vendors/elementor/tutor_widgets/courses.php:135
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:200
#: inc/vendors/elementor/tutor_widgets/instructors.php:86
#: inc/vendors/elementor/widgets/brands.php:114
#: inc/vendors/elementor/widgets/posts.php:139
#: inc/vendors/elementor/woo_widgets/woo_products.php:112
msgid "Slides to Scroll"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:123
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:173
#: inc/vendors/elementor/learnpress_widgets/courses.php:119
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:185
#: inc/vendors/elementor/learnpress_widgets/instructors.php:88
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:173
#: inc/vendors/elementor/tutor_widgets/courses.php:137
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:202
#: inc/vendors/elementor/tutor_widgets/instructors.php:88
#: inc/vendors/elementor/widgets/brands.php:116
#: inc/vendors/elementor/widgets/posts.php:141
#: inc/vendors/elementor/woo_widgets/woo_products.php:114
msgid "Set how many slides are scrolled per swipe."
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:137
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:186
#: inc/vendors/elementor/learnpress_widgets/courses.php:133
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:199
#: inc/vendors/elementor/learnpress_widgets/instructors.php:102
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:186
#: inc/vendors/elementor/tutor_widgets/courses.php:151
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:216
#: inc/vendors/elementor/tutor_widgets/instructors.php:102
#: inc/vendors/elementor/woo_widgets/woo_products.php:128
msgid "Rows"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:140
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:189
#: inc/vendors/elementor/learnpress_widgets/courses.php:136
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:201
#: inc/vendors/elementor/learnpress_widgets/instructors.php:105
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:189
#: inc/vendors/elementor/tutor_widgets/courses.php:154
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:218
#: inc/vendors/elementor/tutor_widgets/instructors.php:105
#: inc/vendors/elementor/woo_widgets/woo_products.php:131
msgid "Enter your rows number here"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:151
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:197
#: inc/vendors/elementor/learnpress_widgets/courses.php:147
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:212
#: inc/vendors/elementor/learnpress_widgets/instructors.php:116
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:197
#: inc/vendors/elementor/tutor_widgets/courses.php:165
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:229
#: inc/vendors/elementor/tutor_widgets/instructors.php:116
#: inc/vendors/elementor/woo_widgets/woo_products.php:142
msgid "Show Navigation"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:153
#: inc/vendors/elementor/event_widgets/events.php:168
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:60
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:199
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:211
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:58
#: inc/vendors/elementor/learnpress_widgets/courses.php:149
#: inc/vendors/elementor/learnpress_widgets/courses.php:164
#: inc/vendors/elementor/learnpress_widgets/courses.php:179
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:214
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:229
#: inc/vendors/elementor/learnpress_widgets/instructors.php:118
#: inc/vendors/elementor/learnpress_widgets/instructors.php:133
#: inc/vendors/elementor/learnpress_widgets/search_form.php:49
#: inc/vendors/elementor/learnpress_widgets/search_form.php:60
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:60
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:199
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:211
#: inc/vendors/elementor/tutor_widgets/category_banner.php:67
#: inc/vendors/elementor/tutor_widgets/courses.php:167
#: inc/vendors/elementor/tutor_widgets/courses.php:182
#: inc/vendors/elementor/tutor_widgets/courses.php:197
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:231
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:246
#: inc/vendors/elementor/tutor_widgets/instructors.php:118
#: inc/vendors/elementor/tutor_widgets/instructors.php:133
#: inc/vendors/elementor/tutor_widgets/search_form.php:49
#: inc/vendors/elementor/tutor_widgets/search_form.php:60
#: inc/vendors/elementor/widgets/brands.php:134
#: inc/vendors/elementor/widgets/brands.php:148
#: inc/vendors/elementor/widgets/posts.php:159
#: inc/vendors/elementor/widgets/posts.php:173
#: inc/vendors/elementor/widgets/testimonials.php:133
#: inc/vendors/elementor/widgets/testimonials.php:144
#: inc/vendors/elementor/woo_widgets/woo_products.php:144
#: inc/vendors/elementor/woo_widgets/woo_products.php:159
msgid "Show"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:154
#: inc/vendors/elementor/event_widgets/events.php:169
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:59
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:200
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:212
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:57
#: inc/vendors/elementor/learnpress_widgets/courses.php:150
#: inc/vendors/elementor/learnpress_widgets/courses.php:165
#: inc/vendors/elementor/learnpress_widgets/courses.php:180
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:215
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:230
#: inc/vendors/elementor/learnpress_widgets/instructors.php:119
#: inc/vendors/elementor/learnpress_widgets/instructors.php:134
#: inc/vendors/elementor/learnpress_widgets/search_form.php:48
#: inc/vendors/elementor/learnpress_widgets/search_form.php:59
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:59
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:200
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:212
#: inc/vendors/elementor/tutor_widgets/category_banner.php:66
#: inc/vendors/elementor/tutor_widgets/courses.php:168
#: inc/vendors/elementor/tutor_widgets/courses.php:183
#: inc/vendors/elementor/tutor_widgets/courses.php:198
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:232
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:247
#: inc/vendors/elementor/tutor_widgets/instructors.php:119
#: inc/vendors/elementor/tutor_widgets/instructors.php:134
#: inc/vendors/elementor/tutor_widgets/search_form.php:48
#: inc/vendors/elementor/tutor_widgets/search_form.php:59
#: inc/vendors/elementor/widgets/brands.php:133
#: inc/vendors/elementor/widgets/brands.php:147
#: inc/vendors/elementor/widgets/posts.php:158
#: inc/vendors/elementor/widgets/posts.php:172
#: inc/vendors/elementor/widgets/testimonials.php:132
#: inc/vendors/elementor/widgets/testimonials.php:143
#: inc/vendors/elementor/woo_widgets/woo_products.php:145
#: inc/vendors/elementor/woo_widgets/woo_products.php:160
msgid "Hide"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:166
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:209
#: inc/vendors/elementor/learnpress_widgets/courses.php:162
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:227
#: inc/vendors/elementor/learnpress_widgets/instructors.php:131
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:209
#: inc/vendors/elementor/tutor_widgets/courses.php:180
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:244
#: inc/vendors/elementor/tutor_widgets/instructors.php:131
#: inc/vendors/elementor/widgets/brands.php:144
#: inc/vendors/elementor/widgets/posts.php:169
#: inc/vendors/elementor/widgets/testimonials.php:140
#: inc/vendors/elementor/woo_widgets/woo_products.php:157
msgid "Show Pagination"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:181
#: inc/vendors/elementor/learnpress_widgets/instructors.php:146
#: inc/vendors/elementor/tutor_widgets/instructors.php:146
msgid "Position Pagination"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:184
#: inc/vendors/elementor/header-widgets/logo.php:67
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:92
#: inc/vendors/elementor/learnpress_widgets/instructors.php:149
#: inc/vendors/elementor/learnpress_widgets/user_info.php:62
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:109
#: inc/vendors/elementor/tutor_widgets/instructors.php:149
#: inc/vendors/elementor/tutor_widgets/user_info.php:62
#: inc/vendors/elementor/widgets/achievements.php:124
#: inc/vendors/elementor/widgets/banner.php:54
#: inc/vendors/elementor/widgets/banner.php:133
#: inc/vendors/elementor/widgets/features_box.php:179
#: inc/vendors/elementor/widgets/heading.php:191
#: inc/vendors/elementor/widgets/nav_menu.php:107
#: inc/vendors/elementor/widgets/social_links.php:336
msgid "Center"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:185
#: inc/vendors/elementor/header-widgets/logo.php:63
#: inc/vendors/elementor/header-widgets/nav_bar.php:80
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:93
#: inc/vendors/elementor/learnpress_widgets/instructors.php:150
#: inc/vendors/elementor/learnpress_widgets/user_info.php:58
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:110
#: inc/vendors/elementor/tutor_widgets/instructors.php:150
#: inc/vendors/elementor/tutor_widgets/user_info.php:58
#: inc/vendors/elementor/widgets/achievements.php:120
#: inc/vendors/elementor/widgets/banner.php:50
#: inc/vendors/elementor/widgets/banner.php:129
#: inc/vendors/elementor/widgets/features_box.php:175
#: inc/vendors/elementor/widgets/heading.php:187
#: inc/vendors/elementor/widgets/nav_menu.php:103
#: inc/vendors/elementor/widgets/social_links.php:332
msgid "Left"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:186
#: inc/vendors/elementor/header-widgets/logo.php:71
#: inc/vendors/elementor/header-widgets/nav_bar.php:79
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:94
#: inc/vendors/elementor/learnpress_widgets/instructors.php:151
#: inc/vendors/elementor/learnpress_widgets/user_info.php:66
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:111
#: inc/vendors/elementor/tutor_widgets/instructors.php:151
#: inc/vendors/elementor/tutor_widgets/user_info.php:66
#: inc/vendors/elementor/widgets/achievements.php:128
#: inc/vendors/elementor/widgets/banner.php:58
#: inc/vendors/elementor/widgets/banner.php:137
#: inc/vendors/elementor/widgets/features_box.php:183
#: inc/vendors/elementor/widgets/heading.php:195
#: inc/vendors/elementor/widgets/nav_menu.php:111
#: inc/vendors/elementor/widgets/social_links.php:340
msgid "Right"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:199
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:221
#: inc/vendors/elementor/learnpress_widgets/courses.php:192
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:242
#: inc/vendors/elementor/learnpress_widgets/instructors.php:163
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:221
#: inc/vendors/elementor/tutor_widgets/courses.php:210
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:259
#: inc/vendors/elementor/tutor_widgets/instructors.php:163
#: inc/vendors/elementor/woo_widgets/woo_products.php:172
msgid "Autoplay"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:214
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:233
#: inc/vendors/elementor/learnpress_widgets/courses.php:207
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:257
#: inc/vendors/elementor/learnpress_widgets/instructors.php:178
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:233
#: inc/vendors/elementor/tutor_widgets/courses.php:225
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:274
#: inc/vendors/elementor/tutor_widgets/instructors.php:178
#: inc/vendors/elementor/woo_widgets/woo_products.php:187
msgid "Infinite Loop"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:229
#: inc/vendors/elementor/learnpress_widgets/courses.php:222
#: inc/vendors/elementor/learnpress_widgets/instructors.php:193
#: inc/vendors/elementor/tutor_widgets/courses.php:240
#: inc/vendors/elementor/tutor_widgets/instructors.php:193
#: inc/vendors/elementor/widgets/testimonials.php:166
msgid "Full Screen"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:244
#: inc/vendors/elementor/header-widgets/logo.php:84
#: inc/vendors/elementor/header-widgets/nav_bar.php:89
#: inc/vendors/elementor/header-widgets/primary_menu.php:36
#: inc/vendors/elementor/header-widgets/vertical_menu.php:79
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:245
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:165
#: inc/vendors/elementor/learnpress_widgets/courses.php:237
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:272
#: inc/vendors/elementor/learnpress_widgets/instructors.php:208
#: inc/vendors/elementor/learnpress_widgets/my-wishlist.php:43
#: inc/vendors/elementor/learnpress_widgets/search_form.php:98
#: inc/vendors/elementor/learnpress_widgets/user_info.php:45
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:245
#: inc/vendors/elementor/tutor_widgets/category_banner.php:174
#: inc/vendors/elementor/tutor_widgets/courses.php:255
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:289
#: inc/vendors/elementor/tutor_widgets/instructors.php:208
#: inc/vendors/elementor/tutor_widgets/my-wishlist.php:43
#: inc/vendors/elementor/tutor_widgets/search_form.php:98
#: inc/vendors/elementor/tutor_widgets/user_info.php:45
#: inc/vendors/elementor/widgets/achievements.php:146
#: inc/vendors/elementor/widgets/address_box.php:170
#: inc/vendors/elementor/widgets/banner.php:182
#: inc/vendors/elementor/widgets/banners.php:101
#: inc/vendors/elementor/widgets/banner_account.php:52
#: inc/vendors/elementor/widgets/brands.php:172
#: inc/vendors/elementor/widgets/call_to_action.php:95
#: inc/vendors/elementor/widgets/countdown.php:58
#: inc/vendors/elementor/widgets/features_box.php:201
#: inc/vendors/elementor/widgets/list_icon.php:147
#: inc/vendors/elementor/widgets/mailchimp.php:49
#: inc/vendors/elementor/widgets/nav_menu.php:129
#: inc/vendors/elementor/widgets/popup_video.php:71
#: inc/vendors/elementor/widgets/posts.php:192
#: inc/vendors/elementor/widgets/revslider.php:54
#: inc/vendors/elementor/widgets/social_links.php:196
#: inc/vendors/elementor/widgets/tabs.php:158
#: inc/vendors/elementor/widgets/team.php:174
#: inc/vendors/elementor/widgets/testimonials.php:179
#: inc/vendors/elementor/woo_widgets/woo_header_cart.php:37
#: inc/vendors/elementor/woo_widgets/woo_products.php:202
msgid "Extra class name"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:246
#: inc/vendors/elementor/header-widgets/logo.php:86
#: inc/vendors/elementor/header-widgets/nav_bar.php:91
#: inc/vendors/elementor/header-widgets/primary_menu.php:38
#: inc/vendors/elementor/header-widgets/vertical_menu.php:81
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:247
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:167
#: inc/vendors/elementor/learnpress_widgets/courses.php:239
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:274
#: inc/vendors/elementor/learnpress_widgets/instructors.php:210
#: inc/vendors/elementor/learnpress_widgets/my-wishlist.php:45
#: inc/vendors/elementor/learnpress_widgets/search_form.php:100
#: inc/vendors/elementor/learnpress_widgets/user_info.php:47
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:247
#: inc/vendors/elementor/tutor_widgets/category_banner.php:176
#: inc/vendors/elementor/tutor_widgets/courses.php:257
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:291
#: inc/vendors/elementor/tutor_widgets/instructors.php:210
#: inc/vendors/elementor/tutor_widgets/my-wishlist.php:45
#: inc/vendors/elementor/tutor_widgets/search_form.php:100
#: inc/vendors/elementor/tutor_widgets/user_info.php:47
#: inc/vendors/elementor/widgets/achievements.php:148
#: inc/vendors/elementor/widgets/address_box.php:172
#: inc/vendors/elementor/widgets/banner.php:184
#: inc/vendors/elementor/widgets/banners.php:103
#: inc/vendors/elementor/widgets/banner_account.php:54
#: inc/vendors/elementor/widgets/brands.php:174
#: inc/vendors/elementor/widgets/call_to_action.php:97
#: inc/vendors/elementor/widgets/countdown.php:60
#: inc/vendors/elementor/widgets/features_box.php:203
#: inc/vendors/elementor/widgets/list_icon.php:149
#: inc/vendors/elementor/widgets/mailchimp.php:51
#: inc/vendors/elementor/widgets/nav_menu.php:131
#: inc/vendors/elementor/widgets/popup_video.php:73
#: inc/vendors/elementor/widgets/posts.php:194
#: inc/vendors/elementor/widgets/revslider.php:56
#: inc/vendors/elementor/widgets/social_links.php:198
#: inc/vendors/elementor/widgets/tabs.php:160
#: inc/vendors/elementor/widgets/team.php:176
#: inc/vendors/elementor/widgets/testimonials.php:181
#: inc/vendors/elementor/woo_widgets/woo_header_cart.php:39
#: inc/vendors/elementor/woo_widgets/woo_products.php:204
msgid "If you wish to style particular content element differently, please add a class name to this field and refer to it in your custom CSS file."
msgstr ""

#: inc/vendors/elementor/functions.php:35
msgid "Educrat Elements"
msgstr ""

#: inc/vendors/elementor/functions.php:43
msgid "Educrat Header Elements"
msgstr ""

#: inc/vendors/elementor/functions.php:180
msgid "Scale"
msgstr ""

#: inc/vendors/elementor/functions.php:181
msgid "Fancy"
msgstr ""

#: inc/vendors/elementor/functions.php:182
msgid "Slide Up"
msgstr ""

#: inc/vendors/elementor/functions.php:183
msgid "Slide Left"
msgstr ""

#: inc/vendors/elementor/functions.php:184
msgid "Slide Right"
msgstr ""

#: inc/vendors/elementor/functions.php:185
msgid "Slide Down"
msgstr ""

#: inc/vendors/elementor/header-widgets/logo.php:16
msgid "Apus Header Logo"
msgstr ""

#: inc/vendors/elementor/header-widgets/logo.php:28
#: inc/vendors/elementor/header-widgets/nav_bar.php:28
#: inc/vendors/elementor/header-widgets/primary_menu.php:28
#: inc/vendors/elementor/header-widgets/vertical_menu.php:28
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:31
#: inc/vendors/elementor/learnpress_widgets/my-wishlist.php:25
#: inc/vendors/elementor/learnpress_widgets/search_form.php:28
#: inc/vendors/elementor/learnpress_widgets/user_info.php:28
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:31
#: inc/vendors/elementor/tutor_widgets/my-wishlist.php:25
#: inc/vendors/elementor/tutor_widgets/search_form.php:28
#: inc/vendors/elementor/tutor_widgets/user_info.php:28
#: inc/vendors/elementor/widgets/achievements.php:92
#: inc/vendors/elementor/widgets/address_box.php:116
#: inc/vendors/elementor/widgets/banner.php:86
#: inc/vendors/elementor/widgets/banners.php:28
#: inc/vendors/elementor/widgets/brands.php:28
#: inc/vendors/elementor/widgets/call_to_action.php:28
#: inc/vendors/elementor/widgets/features_box.php:105
#: inc/vendors/elementor/widgets/list_icon.php:105
#: inc/vendors/elementor/widgets/popup_video.php:32
#: inc/vendors/elementor/widgets/social_links.php:32
#: inc/vendors/elementor/widgets/tabs.php:107
#: inc/vendors/elementor/widgets/testimonials.php:32
#: inc/vendors/elementor/widgets/testimonials.php:70
#: inc/vendors/elementor/woo_widgets/woo_header_cart.php:28
#: inc/vendors/elementor/woo_widgets/woo_products.php:32
msgid "Content"
msgstr ""

#: inc/vendors/elementor/header-widgets/logo.php:36
msgid "Main Logo"
msgstr ""

#: inc/vendors/elementor/header-widgets/logo.php:59
#: inc/vendors/elementor/learnpress_widgets/user_info.php:54
#: inc/vendors/elementor/tutor_widgets/user_info.php:54
#: inc/vendors/elementor/widgets/achievements.php:116
#: inc/vendors/elementor/widgets/features_box.php:171
#: inc/vendors/elementor/widgets/heading.php:183
#: inc/vendors/elementor/widgets/nav_menu.php:99
#: inc/vendors/elementor/widgets/social_links.php:328
msgid "Alignment"
msgstr ""

#: inc/vendors/elementor/header-widgets/nav_bar.php:16
msgid "Apus Header NavBar"
msgstr ""

#: inc/vendors/elementor/header-widgets/nav_bar.php:51
#: inc/vendors/elementor/widgets/tabs.php:65
#: inc/widgets/filter-category.php:25
#: inc/widgets/filter-instructor.php:25
#: inc/widgets/filter-level.php:25
#: inc/widgets/filter-price.php:25
#: inc/widgets/filter-rating.php:25
msgid "Select"
msgstr ""

#: inc/vendors/elementor/header-widgets/nav_bar.php:64
#: inc/vendors/elementor/widgets/tabs.php:92
msgid "Choose Template"
msgstr ""

#: inc/vendors/elementor/header-widgets/nav_bar.php:76
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:141
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:129
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:141
#: inc/vendors/elementor/tutor_widgets/category_banner.php:138
#: inc/vendors/elementor/widgets/achievements.php:158
#: inc/vendors/elementor/widgets/address_box.php:156
#: inc/vendors/elementor/widgets/banner.php:155
#: inc/vendors/elementor/widgets/brands.php:158
#: inc/vendors/elementor/widgets/countdown.php:46
#: inc/vendors/elementor/widgets/countdown.php:69
#: inc/vendors/elementor/widgets/features_box.php:147
#: inc/vendors/elementor/widgets/list_icon.php:135
#: inc/vendors/elementor/widgets/mailchimp.php:36
#: inc/vendors/elementor/widgets/nav_menu.php:65
#: inc/vendors/elementor/widgets/popup_video.php:58
#: inc/vendors/elementor/widgets/social_links.php:183
#: inc/vendors/elementor/widgets/tabs.php:141
msgid "Style"
msgstr ""

#: inc/vendors/elementor/header-widgets/nav_bar.php:100
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:70
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:80
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:68
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:78
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:70
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:80
#: inc/vendors/elementor/tutor_widgets/category_banner.php:77
#: inc/vendors/elementor/tutor_widgets/category_banner.php:87
#: inc/vendors/elementor/widgets/achievements.php:43
#: inc/vendors/elementor/widgets/achievements.php:53
#: inc/vendors/elementor/widgets/address_box.php:45
#: inc/vendors/elementor/widgets/address_box.php:55
#: inc/vendors/elementor/widgets/features_box.php:45
#: inc/vendors/elementor/widgets/features_box.php:55
#: inc/vendors/elementor/widgets/list_icon.php:45
#: inc/vendors/elementor/widgets/list_icon.php:55
#: inc/vendors/elementor/widgets/social_links.php:59
#: inc/vendors/elementor/woo_widgets/woo_header_cart.php:48
msgid "Icon"
msgstr ""

#: inc/vendors/elementor/header-widgets/nav_bar.php:108
msgid "Color Icon"
msgstr ""

#: inc/vendors/elementor/header-widgets/nav_bar.php:151
#: inc/vendors/elementor/widgets/tabs.php:220
msgid "Edit Template"
msgstr ""

#: inc/vendors/elementor/header-widgets/nav_bar.php:169
#: inc/vendors/elementor/widgets/tabs.php:249
#: widgets/elementor-template.php:19
msgid "Template is not defined."
msgstr ""

#: inc/vendors/elementor/header-widgets/nav_bar.php:173
#: inc/vendors/elementor/widgets/tabs.php:253
#: widgets/elementor-template.php:16
msgid "The tabs are working. Please, note, that you have to add a template to the library in order to be able to display it inside the tabs."
msgstr ""

#: inc/vendors/elementor/header-widgets/nav_bar.php:179
#: inc/vendors/elementor/widgets/tabs.php:259
msgid "You Haven’t Saved Templates Yet."
msgstr ""

#: inc/vendors/elementor/header-widgets/nav_bar.php:181
#: inc/vendors/elementor/widgets/tabs.php:261
msgid "What is Library?"
msgstr ""

#: inc/vendors/elementor/header-widgets/nav_bar.php:182
#: inc/vendors/elementor/widgets/tabs.php:262
msgid "Read our tutorial on using Library templates."
msgstr ""

#: inc/vendors/elementor/header-widgets/primary_menu.php:16
msgid "Apus Header Primary Menu"
msgstr ""

#: inc/vendors/elementor/header-widgets/primary_menu.php:55
msgid "Effect Dropdown Menu"
msgstr ""

#: inc/vendors/elementor/header-widgets/primary_menu.php:58
msgid "Effect 1"
msgstr ""

#: inc/vendors/elementor/header-widgets/primary_menu.php:59
msgid "Effect 2"
msgstr ""

#: inc/vendors/elementor/header-widgets/primary_menu.php:60
msgid "Effect 3"
msgstr ""

#: inc/vendors/elementor/header-widgets/primary_menu.php:69
msgid "Padding Menu"
msgstr ""

#: inc/vendors/elementor/header-widgets/primary_menu.php:80
msgid "Color Menu"
msgstr ""

#: inc/vendors/elementor/header-widgets/primary_menu.php:91
msgid "Color Hover Menu"
msgstr ""

#: inc/vendors/elementor/header-widgets/primary_menu.php:112
msgid "Color Dropdown Menu"
msgstr ""

#: inc/vendors/elementor/header-widgets/primary_menu.php:123
msgid "Color Hover Dropdown Menu"
msgstr ""

#: inc/vendors/elementor/header-widgets/primary_menu.php:134
msgid "Background Color Dropdown Menu"
msgstr ""

#: inc/vendors/elementor/header-widgets/vertical_menu.php:16
msgid "Apus Header Vertical Menu"
msgstr ""

#: inc/vendors/elementor/header-widgets/vertical_menu.php:39
#: inc/vendors/elementor/learnpress_widgets/my-wishlist.php:36
#: inc/vendors/elementor/tutor_widgets/my-wishlist.php:36
#: inc/vendors/elementor/widgets/brands.php:39
#: inc/vendors/elementor/widgets/call_to_action.php:39
#: inc/vendors/elementor/widgets/nav_menu.php:48
#: inc/vendors/elementor/widgets/popup_video.php:42
#: inc/vendors/elementor/widgets/posts.php:37
msgid "Enter your title here"
msgstr ""

#: inc/vendors/elementor/header-widgets/vertical_menu.php:56
#: inc/vendors/elementor/widgets/nav_menu.php:55
msgid "Menu"
msgstr ""

#: inc/vendors/elementor/header-widgets/vertical_menu.php:70
msgid "Position Sidebar"
msgstr ""

#: inc/vendors/elementor/header-widgets/vertical_menu.php:91
msgid "Style Menu"
msgstr ""

#: inc/vendors/elementor/header-widgets/vertical_menu.php:99
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:484
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:516
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:404
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:436
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:291
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:465
#: inc/vendors/elementor/learnpress_widgets/instructors.php:262
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:484
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:516
#: inc/vendors/elementor/tutor_widgets/category_banner.php:413
#: inc/vendors/elementor/tutor_widgets/category_banner.php:445
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:308
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:482
#: inc/vendors/elementor/tutor_widgets/instructors.php:261
#: inc/vendors/elementor/widgets/achievements.php:186
#: inc/vendors/elementor/widgets/address_box.php:345
#: inc/vendors/elementor/widgets/address_box.php:377
#: inc/vendors/elementor/widgets/call_to_action.php:117
#: inc/vendors/elementor/widgets/list_icon.php:187
#: inc/vendors/elementor/widgets/nav_menu.php:150
#: inc/vendors/elementor/widgets/posts.php:213
#: inc/vendors/elementor/widgets/social_links.php:215
#: inc/vendors/elementor/widgets/testimonials.php:217
#: inc/vendors/elementor/woo_widgets/woo_products.php:287
msgid "Title Color"
msgstr ""

#: inc/vendors/elementor/header-widgets/vertical_menu.php:115
#: inc/vendors/elementor/header-widgets/vertical_menu.php:197
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:266
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:343
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:477
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:186
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:263
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:397
#: inc/vendors/elementor/learnpress_widgets/courses.php:259
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:315
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:523
#: inc/vendors/elementor/learnpress_widgets/search_form.php:185
#: inc/vendors/elementor/learnpress_widgets/user_info.php:91
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:266
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:343
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:477
#: inc/vendors/elementor/tutor_widgets/category_banner.php:195
#: inc/vendors/elementor/tutor_widgets/category_banner.php:272
#: inc/vendors/elementor/tutor_widgets/category_banner.php:406
#: inc/vendors/elementor/tutor_widgets/courses.php:276
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:332
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:540
#: inc/vendors/elementor/tutor_widgets/search_form.php:185
#: inc/vendors/elementor/tutor_widgets/user_info.php:91
#: inc/vendors/elementor/widgets/address_box.php:193
#: inc/vendors/elementor/widgets/address_box.php:338
#: inc/vendors/elementor/widgets/features_box.php:224
#: inc/vendors/elementor/widgets/features_box.php:390
#: inc/vendors/elementor/widgets/heading.php:268
#: inc/vendors/elementor/widgets/mailchimp.php:187
#: inc/vendors/elementor/widgets/social_links.php:187
#: inc/vendors/elementor/widgets/social_links.php:248
msgid "Normal"
msgstr ""

#: inc/vendors/elementor/header-widgets/vertical_menu.php:122
#: inc/vendors/elementor/header-widgets/vertical_menu.php:155
#: inc/vendors/elementor/header-widgets/vertical_menu.php:204
#: inc/vendors/elementor/header-widgets/vertical_menu.php:226
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:350
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:382
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:270
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:302
#: inc/vendors/elementor/learnpress_widgets/courses.php:266
#: inc/vendors/elementor/learnpress_widgets/courses.php:316
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:322
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:354
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:530
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:580
#: inc/vendors/elementor/learnpress_widgets/search_form.php:192
#: inc/vendors/elementor/learnpress_widgets/search_form.php:224
#: inc/vendors/elementor/learnpress_widgets/user_info.php:98
#: inc/vendors/elementor/learnpress_widgets/user_info.php:139
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:350
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:382
#: inc/vendors/elementor/tutor_widgets/category_banner.php:279
#: inc/vendors/elementor/tutor_widgets/category_banner.php:311
#: inc/vendors/elementor/tutor_widgets/courses.php:283
#: inc/vendors/elementor/tutor_widgets/courses.php:333
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:339
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:371
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:547
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:597
#: inc/vendors/elementor/tutor_widgets/search_form.php:192
#: inc/vendors/elementor/tutor_widgets/search_form.php:224
#: inc/vendors/elementor/tutor_widgets/user_info.php:98
#: inc/vendors/elementor/tutor_widgets/user_info.php:139
#: inc/vendors/elementor/widgets/countdown.php:77
#: inc/vendors/elementor/widgets/features_box.php:231
#: inc/vendors/elementor/widgets/features_box.php:292
#: inc/vendors/elementor/widgets/features_box.php:396
#: inc/vendors/elementor/widgets/social_links.php:255
#: inc/vendors/elementor/widgets/social_links.php:289
#: inc/vendors/elementor/widgets/testimonials.php:317
msgid "Color"
msgstr ""

#: inc/vendors/elementor/header-widgets/vertical_menu.php:134
#: inc/vendors/elementor/header-widgets/vertical_menu.php:168
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:273
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:303
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:361
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:393
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:193
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:223
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:281
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:313
#: inc/vendors/elementor/learnpress_widgets/courses.php:277
#: inc/vendors/elementor/learnpress_widgets/courses.php:328
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:333
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:367
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:541
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:592
#: inc/vendors/elementor/learnpress_widgets/search_form.php:203
#: inc/vendors/elementor/learnpress_widgets/search_form.php:236
#: inc/vendors/elementor/learnpress_widgets/user_info.php:109
#: inc/vendors/elementor/learnpress_widgets/user_info.php:150
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:273
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:303
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:361
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:393
#: inc/vendors/elementor/tutor_widgets/category_banner.php:202
#: inc/vendors/elementor/tutor_widgets/category_banner.php:232
#: inc/vendors/elementor/tutor_widgets/category_banner.php:290
#: inc/vendors/elementor/tutor_widgets/category_banner.php:322
#: inc/vendors/elementor/tutor_widgets/courses.php:294
#: inc/vendors/elementor/tutor_widgets/courses.php:345
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:350
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:384
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:558
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:609
#: inc/vendors/elementor/tutor_widgets/search_form.php:203
#: inc/vendors/elementor/tutor_widgets/search_form.php:236
#: inc/vendors/elementor/tutor_widgets/user_info.php:109
#: inc/vendors/elementor/tutor_widgets/user_info.php:150
#: inc/vendors/elementor/widgets/social_links.php:267
#: inc/vendors/elementor/widgets/social_links.php:302
msgid "Background Color"
msgstr ""

#: inc/vendors/elementor/header-widgets/vertical_menu.php:148
#: inc/vendors/elementor/header-widgets/vertical_menu.php:219
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:296
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:375
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:509
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:216
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:295
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:429
#: inc/vendors/elementor/learnpress_widgets/courses.php:309
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:347
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:573
#: inc/vendors/elementor/learnpress_widgets/search_form.php:217
#: inc/vendors/elementor/learnpress_widgets/user_info.php:132
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:296
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:375
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:509
#: inc/vendors/elementor/tutor_widgets/category_banner.php:225
#: inc/vendors/elementor/tutor_widgets/category_banner.php:304
#: inc/vendors/elementor/tutor_widgets/category_banner.php:438
#: inc/vendors/elementor/tutor_widgets/courses.php:326
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:364
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:590
#: inc/vendors/elementor/tutor_widgets/search_form.php:217
#: inc/vendors/elementor/tutor_widgets/user_info.php:132
#: inc/vendors/elementor/widgets/address_box.php:234
#: inc/vendors/elementor/widgets/address_box.php:370
#: inc/vendors/elementor/widgets/features_box.php:285
#: inc/vendors/elementor/widgets/features_box.php:427
#: inc/vendors/elementor/widgets/mailchimp.php:230
#: inc/vendors/elementor/widgets/social_links.php:282
msgid "Hover"
msgstr ""

#: inc/vendors/elementor/header-widgets/vertical_menu.php:187
msgid "Style Menu Inner"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:16
msgid "Apus LearnPress Categories Banner"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:28
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:28
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:28
#: inc/vendors/elementor/tutor_widgets/category_banner.php:28
msgid "Category Banner"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:38
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:36
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:62
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:38
#: inc/vendors/elementor/tutor_widgets/category_banner.php:36
msgid "Category Slug"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:40
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:38
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:40
#: inc/vendors/elementor/tutor_widgets/category_banner.php:38
msgid "Enter your Category Slug here"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:47
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:45
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:47
#: inc/vendors/elementor/tutor_widgets/category_banner.php:45
msgid "Category Title"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:49
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:47
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:49
#: inc/vendors/elementor/tutor_widgets/category_banner.php:47
msgid "Enter your category title"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:56
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:54
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:56
#: inc/vendors/elementor/tutor_widgets/category_banner.php:63
msgid "Show Number Courses"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:67
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:65
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:67
#: inc/vendors/elementor/tutor_widgets/category_banner.php:74
#: inc/vendors/elementor/widgets/achievements.php:40
#: inc/vendors/elementor/widgets/address_box.php:42
#: inc/vendors/elementor/widgets/features_box.php:42
#: inc/vendors/elementor/widgets/list_icon.php:42
msgid "Image or Icon"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:92
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:90
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:92
#: inc/vendors/elementor/tutor_widgets/category_banner.php:99
#: inc/vendors/elementor/widgets/achievements.php:65
#: inc/vendors/elementor/widgets/address_box.php:67
#: inc/vendors/elementor/widgets/features_box.php:67
#: inc/vendors/elementor/widgets/list_icon.php:67
#: inc/vendors/elementor/widgets/testimonials.php:43
msgid "Choose Image"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:109
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:119
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:109
#: inc/vendors/elementor/tutor_widgets/category_banner.php:128
msgid "Custom URL"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:112
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:122
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:112
#: inc/vendors/elementor/tutor_widgets/category_banner.php:131
msgid "Enter your custom category link here"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:119
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:119
#: tutor/course-filter/filters.php:29
msgid "Categories"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:121
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:121
msgid "Enter your categories here"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:144
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:132
#: inc/vendors/elementor/learnpress_widgets/instructors.php:49
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:144
#: inc/vendors/elementor/tutor_widgets/category_banner.php:141
#: inc/vendors/elementor/tutor_widgets/instructors.php:49
#: inc/vendors/elementor/widgets/banner.php:158
#: inc/vendors/elementor/widgets/brands.php:161
#: inc/vendors/elementor/widgets/countdown.php:49
#: inc/vendors/elementor/widgets/features_box.php:150
#: inc/vendors/elementor/widgets/list_icon.php:138
#: inc/vendors/elementor/widgets/popup_video.php:61
#: inc/vendors/elementor/widgets/tabs.php:145
#: inc/vendors/elementor/widgets/testimonials.php:154
#: inc/widgets/socials.php:28
msgid "Style 1"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:145
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:133
#: inc/vendors/elementor/learnpress_widgets/instructors.php:50
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:145
#: inc/vendors/elementor/tutor_widgets/category_banner.php:142
#: inc/vendors/elementor/tutor_widgets/instructors.php:50
#: inc/vendors/elementor/widgets/banner.php:159
#: inc/vendors/elementor/widgets/brands.php:162
#: inc/vendors/elementor/widgets/features_box.php:151
#: inc/vendors/elementor/widgets/popup_video.php:62
#: inc/vendors/elementor/widgets/tabs.php:146
#: inc/vendors/elementor/widgets/testimonials.php:155
msgid "Style 2"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:146
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:134
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:146
#: inc/vendors/elementor/tutor_widgets/category_banner.php:143
#: inc/vendors/elementor/widgets/banner.php:160
#: inc/vendors/elementor/widgets/brands.php:163
#: inc/vendors/elementor/widgets/tabs.php:147
#: inc/vendors/elementor/widgets/testimonials.php:156
msgid "Style 3"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:147
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:135
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:147
#: inc/vendors/elementor/tutor_widgets/category_banner.php:144
#: inc/vendors/elementor/widgets/banner.php:161
#: inc/vendors/elementor/widgets/tabs.php:148
#: inc/vendors/elementor/widgets/testimonials.php:157
msgid "Style 4"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:148
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:136
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:148
#: inc/vendors/elementor/tutor_widgets/category_banner.php:145
#: inc/vendors/elementor/widgets/tabs.php:149
msgid "Style 5"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:256
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:176
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:256
#: inc/vendors/elementor/tutor_widgets/category_banner.php:185
#: inc/vendors/elementor/widgets/features_box.php:212
#: inc/vendors/elementor/widgets/list_icon.php:158
#: inc/vendors/elementor/woo_widgets/woo_products.php:215
msgid "Box Style"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:285
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:315
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:205
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:235
#: inc/vendors/elementor/learnpress_widgets/courses.php:289
#: inc/vendors/elementor/learnpress_widgets/courses.php:341
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:553
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:605
#: inc/vendors/elementor/learnpress_widgets/search_form.php:142
#: inc/vendors/elementor/learnpress_widgets/search_form.php:281
#: inc/vendors/elementor/learnpress_widgets/user_info.php:121
#: inc/vendors/elementor/learnpress_widgets/user_info.php:162
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:285
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:315
#: inc/vendors/elementor/tutor_widgets/category_banner.php:214
#: inc/vendors/elementor/tutor_widgets/category_banner.php:244
#: inc/vendors/elementor/tutor_widgets/courses.php:306
#: inc/vendors/elementor/tutor_widgets/courses.php:358
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:570
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:622
#: inc/vendors/elementor/tutor_widgets/search_form.php:142
#: inc/vendors/elementor/tutor_widgets/search_form.php:281
#: inc/vendors/elementor/tutor_widgets/user_info.php:121
#: inc/vendors/elementor/tutor_widgets/user_info.php:162
#: inc/vendors/elementor/widgets/features_box.php:264
#: inc/vendors/elementor/widgets/features_box.php:313
#: inc/vendors/elementor/widgets/mailchimp.php:82
#: inc/vendors/elementor/woo_widgets/woo_products.php:245
msgid "Border"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:331
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:251
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:331
#: inc/vendors/elementor/tutor_widgets/category_banner.php:260
#: inc/vendors/elementor/widgets/address_box.php:181
#: inc/vendors/elementor/widgets/features_box.php:379
msgid "Icon Style"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:410
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:330
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:386
#: inc/vendors/elementor/learnpress_widgets/search_form.php:150
#: inc/vendors/elementor/learnpress_widgets/search_form.php:289
#: inc/vendors/elementor/learnpress_widgets/user_info.php:187
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:410
#: inc/vendors/elementor/tutor_widgets/category_banner.php:339
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:403
#: inc/vendors/elementor/tutor_widgets/search_form.php:150
#: inc/vendors/elementor/tutor_widgets/search_form.php:289
#: inc/vendors/elementor/tutor_widgets/user_info.php:187
#: inc/vendors/elementor/widgets/mailchimp.php:90
#: inc/vendors/elementor/widgets/mailchimp.php:289
msgid "Border Radius"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:422
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:342
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:422
#: inc/vendors/elementor/tutor_widgets/category_banner.php:351
msgid "Width"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:439
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:145
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:359
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:439
#: inc/vendors/elementor/tutor_widgets/category_banner.php:154
#: inc/vendors/elementor/tutor_widgets/category_banner.php:368
msgid "Height"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:467
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:387
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:412
#: inc/vendors/elementor/learnpress_widgets/instructors.php:220
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:467
#: inc/vendors/elementor/tutor_widgets/category_banner.php:396
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:429
#: inc/vendors/elementor/tutor_widgets/instructors.php:219
#: inc/vendors/elementor/widgets/address_box.php:327
#: inc/vendors/elementor/widgets/features_box.php:338
msgid "Information Style"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:495
#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:527
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:415
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:447
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:495
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:527
#: inc/vendors/elementor/tutor_widgets/category_banner.php:424
#: inc/vendors/elementor/tutor_widgets/category_banner.php:456
#: inc/vendors/elementor/widgets/address_box.php:303
msgid "Number Color"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/categories_banner.php:649
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:539
#: inc/vendors/elementor/tutor_widgets/categories_banner.php:648
#: inc/vendors/elementor/tutor_widgets/category_banner.php:549
msgid "<span>%d</span> Course"
msgid_plural "<span>%d</span> Courses"
msgstr[0] ""
msgstr[1] ""

#: inc/vendors/elementor/learnpress_widgets/category_banner.php:16
msgid "Apus LearnPress Category Banner"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses.php:15
msgid "Apus Learnpress Courses"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses.php:27
#: inc/vendors/elementor/tutor_widgets/courses.php:27
#: tutor/instructor/default.php:91
#: tutor/public-profile.php:107
#: tutor/public-profile.php:168
#: tutor/single/course/instructors.php:64
msgid "Courses"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses.php:34
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:48
msgid "Get Courses By"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses.php:37
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:51
msgid "Recent Courses"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses.php:38
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:52
msgid "Featured Courses"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses.php:39
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:53
msgid "Popular Courses"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses.php:70
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:136
#: inc/vendors/elementor/tutor_widgets/courses.php:88
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:153
msgid "Course Style"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses.php:73
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:139
#: inc/vendors/elementor/tutor_widgets/courses.php:91
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:156
#: inc/vendors/learnpress/customizer.php:224
#: inc/vendors/tutor/customizer.php:194
msgid "Grid 1"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses.php:74
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:140
#: inc/vendors/elementor/tutor_widgets/courses.php:92
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:157
#: inc/vendors/learnpress/customizer.php:225
#: inc/vendors/tutor/customizer.php:195
msgid "Grid 2"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses.php:75
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:141
#: inc/vendors/elementor/tutor_widgets/courses.php:93
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:158
#: inc/vendors/learnpress/customizer.php:226
#: inc/vendors/tutor/customizer.php:196
msgid "Grid 3"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses.php:76
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:142
#: inc/vendors/elementor/tutor_widgets/courses.php:94
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:159
#: inc/vendors/learnpress/customizer.php:227
#: inc/vendors/tutor/customizer.php:197
msgid "Grid 4"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses.php:79
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:145
#: inc/vendors/elementor/tutor_widgets/courses.php:97
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:162
#: inc/vendors/learnpress/customizer.php:230
#: inc/vendors/tutor/customizer.php:200
msgid "List 3"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses.php:80
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:146
#: inc/vendors/elementor/tutor_widgets/courses.php:98
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:163
#: inc/vendors/learnpress/customizer.php:231
#: inc/vendors/tutor/customizer.php:201
msgid "List 4"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses.php:81
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:147
#: inc/vendors/elementor/tutor_widgets/courses.php:99
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:164
#: inc/vendors/learnpress/customizer.php:232
#: inc/vendors/tutor/customizer.php:202
msgid "List 5"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses.php:177
#: inc/vendors/elementor/tutor_widgets/courses.php:195
msgid "Stretch Pagination"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses.php:249
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:513
#: inc/vendors/elementor/tutor_widgets/courses.php:266
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:530
msgid "Arrow Style"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses.php:298
#: inc/vendors/elementor/learnpress_widgets/courses.php:350
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:562
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:614
#: inc/vendors/elementor/learnpress_widgets/search_form.php:163
#: inc/vendors/elementor/tutor_widgets/courses.php:315
#: inc/vendors/elementor/tutor_widgets/courses.php:367
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:579
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:631
#: inc/vendors/elementor/tutor_widgets/search_form.php:163
#: inc/vendors/elementor/widgets/address_box.php:223
#: inc/vendors/elementor/widgets/address_box.php:264
#: inc/vendors/elementor/widgets/features_box.php:273
#: inc/vendors/elementor/widgets/features_box.php:322
#: inc/vendors/elementor/widgets/features_box.php:417
#: inc/vendors/elementor/widgets/features_box.php:455
msgid "Box Shadow"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:15
msgid "Apus Learnpress Courses Tabs"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:40
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:40
msgid "Tab Title"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:89
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:106
msgid "Tabs Position"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:103
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:283
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:120
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:300
msgid "Tabs Style"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:107
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:124
msgid "Gray"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:116
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:133
#: inc/vendors/elementor/widgets/tabs.php:32
#: inc/vendors/elementor/widgets/tabs.php:132
msgid "Tabs"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:118
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:135
msgid "Enter your product tabs here"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:302
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:319
#: inc/vendors/elementor/widgets/achievements.php:207
#: inc/vendors/elementor/widgets/address_box.php:356
#: inc/vendors/elementor/widgets/address_box.php:388
#: inc/vendors/elementor/widgets/call_to_action.php:138
#: inc/vendors/elementor/widgets/list_icon.php:220
msgid "Description Color"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:398
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:415
msgid "Space"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:420
#: inc/vendors/elementor/learnpress_widgets/instructors.php:228
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:437
#: inc/vendors/elementor/tutor_widgets/instructors.php:227
msgid "Item Color"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:431
#: inc/vendors/elementor/learnpress_widgets/instructors.php:239
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:448
#: inc/vendors/elementor/tutor_widgets/instructors.php:238
msgid "Item Link Color"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:442
#: inc/vendors/elementor/learnpress_widgets/instructors.php:250
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:459
#: inc/vendors/elementor/tutor_widgets/instructors.php:249
msgid "Item Link Hover Color"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:454
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:471
msgid "Item Background Color"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:476
#: inc/vendors/elementor/learnpress_widgets/instructors.php:273
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:493
#: inc/vendors/elementor/tutor_widgets/instructors.php:272
#: inc/vendors/elementor/woo_widgets/woo_products.php:299
msgid "Title Hover Color"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:488
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:505
#: inc/vendors/elementor/woo_widgets/woo_products.php:342
msgid "Price Color"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:499
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:516
#: inc/vendors/elementor/widgets/nav_menu.php:70
msgid "Line"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/instructors.php:15
msgid "Apus Learnpress Instructors"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/instructors.php:27
#: inc/vendors/elementor/tutor_widgets/instructors.php:27
#: tutor/single/course/instructors.php:23
msgid "Instructors"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/instructors.php:46
#: inc/vendors/elementor/tutor_widgets/instructors.php:46
msgid "Instructor item style"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/my-wishlist.php:14
msgid "Apus Learnpress My Wishlist"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/my-wishlist.php:94
msgid "No courses found"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/search_form.php:16
msgid "Apus Learnpress Course Search Form"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/search_form.php:36
#: inc/vendors/elementor/tutor_widgets/search_form.php:36
msgid "Input placeholder"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/search_form.php:45
#: inc/vendors/elementor/tutor_widgets/search_form.php:45
msgid "Show Categories Field"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/search_form.php:56
#: inc/vendors/elementor/tutor_widgets/search_form.php:56
msgid "Show Level Field"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/search_form.php:67
#: inc/vendors/elementor/tutor_widgets/search_form.php:67
msgid "Button Icon"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/search_form.php:76
#: inc/vendors/elementor/tutor_widgets/search_form.php:76
#: inc/vendors/elementor/widgets/banner.php:95
#: inc/vendors/elementor/widgets/call_to_action.php:54
#: inc/vendors/elementor/widgets/nav_menu.php:79
msgid "Button Text"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/search_form.php:89
#: inc/vendors/elementor/learnpress_widgets/search_form.php:174
#: inc/vendors/elementor/tutor_widgets/search_form.php:89
#: inc/vendors/elementor/tutor_widgets/search_form.php:174
#: inc/vendors/elementor/widgets/mailchimp.php:177
msgid "Button"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/search_form.php:110
#: inc/vendors/elementor/tutor_widgets/search_form.php:110
msgid "Box"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/search_form.php:118
#: inc/vendors/elementor/tutor_widgets/search_form.php:118
msgid "Icon Search Color"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/search_form.php:129
#: inc/vendors/elementor/learnpress_widgets/search_form.php:268
#: inc/vendors/elementor/learnpress_widgets/user_info.php:175
#: inc/vendors/elementor/tutor_widgets/search_form.php:129
#: inc/vendors/elementor/tutor_widgets/search_form.php:268
#: inc/vendors/elementor/tutor_widgets/user_info.php:175
#: inc/vendors/elementor/widgets/features_box.php:251
#: inc/vendors/elementor/widgets/mailchimp.php:278
msgid "Padding"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/search_form.php:248
#: inc/vendors/elementor/tutor_widgets/search_form.php:248
msgid "Border Color"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/user_info.php:16
msgid "Apus Learnpress Header User Info"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/user_info.php:36
#: inc/vendors/elementor/tutor_widgets/user_info.php:36
msgid "Login Text"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/user_info.php:81
#: inc/vendors/elementor/tutor_widgets/user_info.php:81
#: inc/vendors/elementor/widgets/banner.php:104
#: inc/vendors/elementor/widgets/call_to_action.php:74
msgid "Button Style"
msgstr ""

#: inc/vendors/elementor/tutor_widgets/categories_banner.php:16
msgid "Apus Tutor Categories Banner"
msgstr ""

#: inc/vendors/elementor/tutor_widgets/category_banner.php:16
msgid "Apus Tutor Category Banner"
msgstr ""

#: inc/vendors/elementor/tutor_widgets/category_banner.php:56
#: inc/vendors/elementor/widgets/call_to_action.php:47
msgid "Enter your description here"
msgstr ""

#: inc/vendors/elementor/tutor_widgets/courses.php:15
msgid "Apus Tutor Courses"
msgstr ""

#: inc/vendors/elementor/tutor_widgets/courses.php:35
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:48
msgid "Category ID"
msgstr ""

#: inc/vendors/elementor/tutor_widgets/courses.php:39
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:52
msgid "Place comma (,) separated category ids"
msgstr ""

#: inc/vendors/elementor/tutor_widgets/courses.php:46
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:59
msgid "OrderBy"
msgstr ""

#: inc/vendors/elementor/tutor_widgets/courses.php:50
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:63
#: inc/vendors/elementor/widgets/posts.php:60
msgid "ID"
msgstr ""

#: inc/vendors/elementor/tutor_widgets/courses.php:52
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:65
msgid "Rand"
msgstr ""

#: inc/vendors/elementor/tutor_widgets/courses.php:53
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:66
#: inc/vendors/elementor/widgets/posts.php:59
#: woocommerce/checkout/thankyou.php:50
#: woocommerce/myaccount/my-orders.php:35
#: woocommerce/myaccount/my-orders.php:54
msgid "Date"
msgstr ""

#: inc/vendors/elementor/tutor_widgets/courses.php:54
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:67
#: inc/vendors/elementor/widgets/posts.php:66
msgid "Menu order"
msgstr ""

#: inc/vendors/elementor/tutor_widgets/courses.php:63
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:76
#: woocommerce/myaccount/my-orders.php:34
msgid "Order"
msgstr ""

#: inc/vendors/elementor/tutor_widgets/courses.php:67
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:80
msgid "DESC"
msgstr ""

#: inc/vendors/elementor/tutor_widgets/courses.php:68
#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:81
msgid "ASC"
msgstr ""

#: inc/vendors/elementor/tutor_widgets/courses_tabs.php:15
msgid "Apus Tutor Courses Tabs"
msgstr ""

#: inc/vendors/elementor/tutor_widgets/instructors.php:15
msgid "Apus Tutor Instructors"
msgstr ""

#: inc/vendors/elementor/tutor_widgets/my-wishlist.php:14
msgid "Apus Tutor My Wishlist"
msgstr ""

#: inc/vendors/elementor/tutor_widgets/my-wishlist.php:66
msgid "Please login to view your wishlist"
msgstr ""

#: inc/vendors/elementor/tutor_widgets/search_form.php:16
msgid "Apus Tutor Course Search Form"
msgstr ""

#: inc/vendors/elementor/tutor_widgets/user_info.php:16
msgid "Apus Tutor Header User Info"
msgstr ""

#: inc/vendors/elementor/widgets/achievements.php:16
msgid "Apus Achievements"
msgstr ""

#: inc/vendors/elementor/widgets/achievements.php:32
msgid "Achievements"
msgstr ""

#: inc/vendors/elementor/widgets/achievements.php:84
#: inc/vendors/elementor/widgets/features_box.php:97
msgid "This is the heading"
msgstr ""

#: inc/vendors/elementor/widgets/achievements.php:85
#: inc/vendors/elementor/widgets/features_box.php:98
#: inc/vendors/elementor/widgets/heading.php:113
msgid "Enter your title"
msgstr ""

#: inc/vendors/elementor/widgets/achievements.php:94
msgid "Click edit button to change this text. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper mattis, pulvinar dapibus leo."
msgstr ""

#: inc/vendors/elementor/widgets/achievements.php:95
#: inc/vendors/elementor/widgets/features_box.php:108
msgid "Enter your description"
msgstr ""

#: inc/vendors/elementor/widgets/achievements.php:105
#: inc/vendors/elementor/widgets/address_box.php:127
#: inc/vendors/elementor/widgets/features_box.php:118
#: inc/vendors/elementor/widgets/list_icon.php:116
msgid "Link to"
msgstr ""

#: inc/vendors/elementor/widgets/achievements.php:107
#: inc/vendors/elementor/widgets/address_box.php:129
#: inc/vendors/elementor/widgets/features_box.php:120
#: inc/vendors/elementor/widgets/list_icon.php:118
#: inc/vendors/elementor/widgets/social_links.php:146
#: inc/vendors/elementor/widgets/team.php:129
#: inc/vendors/elementor/widgets/testimonials.php:99
msgid "https://your-link.com"
msgstr ""

#: inc/vendors/elementor/widgets/achievements.php:132
#: inc/vendors/elementor/widgets/banner.php:62
#: inc/vendors/elementor/widgets/banner.php:141
#: inc/vendors/elementor/widgets/features_box.php:187
#: inc/vendors/elementor/widgets/heading.php:199
#: inc/vendors/elementor/widgets/nav_menu.php:115
#: inc/vendors/elementor/widgets/social_links.php:344
msgid "Justified"
msgstr ""

#: inc/vendors/elementor/widgets/achievements.php:165
#: inc/vendors/elementor/widgets/address_box.php:200
#: inc/vendors/elementor/widgets/address_box.php:241
#: inc/vendors/elementor/widgets/list_icon.php:166
#: inc/vendors/elementor/woo_widgets/woo_header_cart.php:56
msgid "Icon Color"
msgstr ""

#: inc/vendors/elementor/widgets/achievements.php:177
#: inc/vendors/elementor/widgets/address_box.php:294
#: inc/vendors/elementor/widgets/list_icon.php:178
msgid "Icon Typography"
msgstr ""

#: inc/vendors/elementor/widgets/achievements.php:198
#: inc/vendors/elementor/widgets/address_box.php:404
#: inc/vendors/elementor/widgets/call_to_action.php:129
#: inc/vendors/elementor/widgets/list_icon.php:199
#: inc/vendors/elementor/widgets/nav_menu.php:162
#: inc/vendors/elementor/widgets/posts.php:225
#: inc/vendors/elementor/widgets/social_links.php:227
#: inc/vendors/elementor/widgets/testimonials.php:228
#: inc/vendors/elementor/woo_widgets/woo_products.php:312
msgid "Title Typography"
msgstr ""

#: inc/vendors/elementor/widgets/achievements.php:219
#: inc/vendors/elementor/widgets/address_box.php:413
#: inc/vendors/elementor/widgets/call_to_action.php:150
#: inc/vendors/elementor/widgets/list_icon.php:232
msgid "Description Typography"
msgstr ""

#: inc/vendors/elementor/widgets/address_box.php:16
#: inc/vendors/elementor/widgets/address_box.php:32
#: inc/vendors/elementor/widgets/address_box.php:137
msgid "Apus Box"
msgstr ""

#: inc/vendors/elementor/widgets/address_box.php:96
#: inc/vendors/elementor/widgets/posts.php:44
msgid "Number"
msgstr ""

#: inc/vendors/elementor/widgets/address_box.php:159
msgid "Style1"
msgstr ""

#: inc/vendors/elementor/widgets/address_box.php:160
msgid "Style2"
msgstr ""

#: inc/vendors/elementor/widgets/address_box.php:161
msgid "Style2 (Number)"
msgstr ""

#: inc/vendors/elementor/widgets/address_box.php:211
#: inc/vendors/elementor/widgets/address_box.php:252
msgid "Icon Background Color"
msgstr ""

#: inc/vendors/elementor/widgets/address_box.php:277
#: inc/vendors/elementor/widgets/heading.php:146
msgid "Size"
msgstr ""

#: inc/vendors/elementor/widgets/address_box.php:314
msgid "Number Background Color"
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:16
msgid "Apus Banner"
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:28
msgid "Banner"
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:39
#: inc/vendors/elementor/widgets/team.php:157
msgid "Upload Image Here"
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:46
msgid "Image Alignment"
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:76
msgid "URL"
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:79
#: inc/vendors/elementor/widgets/call_to_action.php:67
#: inc/vendors/elementor/widgets/nav_menu.php:92
msgid "Enter your Button Link here"
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:88
msgid "Enter your content here"
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:97
#: inc/vendors/elementor/widgets/call_to_action.php:57
#: inc/vendors/elementor/widgets/nav_menu.php:82
msgid "Enter your button text here"
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:108
msgid "Theme Outline Color"
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:109
#: inc/vendors/elementor/widgets/call_to_action.php:77
msgid "Default "
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:110
#: inc/vendors/elementor/widgets/call_to_action.php:78
msgid "Primary "
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:111
#: inc/vendors/elementor/widgets/call_to_action.php:79
msgid "Success "
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:112
#: inc/vendors/elementor/widgets/call_to_action.php:80
msgid "Info "
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:113
#: inc/vendors/elementor/widgets/call_to_action.php:81
msgid "Warning "
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:114
#: inc/vendors/elementor/widgets/call_to_action.php:82
msgid "Danger "
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:115
#: inc/vendors/elementor/widgets/call_to_action.php:83
msgid "Pink "
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:116
#: inc/vendors/elementor/widgets/call_to_action.php:84
msgid "White "
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:125
msgid "Content Alignment"
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:169
msgid "Vertical Content"
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:172
msgid "Top"
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:173
msgid "Middle"
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:174
msgid "Bottom"
msgstr ""

#: inc/vendors/elementor/widgets/banners.php:16
msgid "Apus Banners"
msgstr ""

#: inc/vendors/elementor/widgets/banners.php:39
msgid "Banner Title"
msgstr ""

#: inc/vendors/elementor/widgets/banners.php:48
msgid "Banner Image"
msgstr ""

#: inc/vendors/elementor/widgets/banners.php:50
msgid "Upload Banner Image"
msgstr ""

#: inc/vendors/elementor/widgets/banners.php:57
#: inc/vendors/elementor/widgets/brands.php:67
#: inc/vendors/elementor/widgets/heading.php:131
#: inc/vendors/elementor/widgets/social_links.php:137
msgid "Link"
msgstr ""

#: inc/vendors/elementor/widgets/banners.php:60
msgid "Enter your banner link here"
msgstr ""

#: inc/vendors/elementor/widgets/banners.php:67
msgid "Banners"
msgstr ""

#: inc/vendors/elementor/widgets/banners.php:69
msgid "Enter your banners here"
msgstr ""

#: inc/vendors/elementor/widgets/banners.php:93
msgid "Enter your column number here"
msgstr ""

#: inc/vendors/elementor/widgets/banner_account.php:16
msgid "Apus Banner Create Account"
msgstr ""

#: inc/vendors/elementor/widgets/banner_account.php:28
msgid "Banner Account"
msgstr ""

#: inc/vendors/elementor/widgets/brands.php:16
msgid "Apus Brands"
msgstr ""

#: inc/vendors/elementor/widgets/brands.php:49
msgid "Brand Title"
msgstr ""

#: inc/vendors/elementor/widgets/brands.php:58
msgid "Brand Image"
msgstr ""

#: inc/vendors/elementor/widgets/brands.php:60
#: inc/vendors/elementor/widgets/testimonials.php:45
msgid "Upload Brand Image"
msgstr ""

#: inc/vendors/elementor/widgets/brands.php:70
msgid "Enter your brand link here"
msgstr ""

#: inc/vendors/elementor/widgets/brands.php:77
msgid "Brands"
msgstr ""

#: inc/vendors/elementor/widgets/brands.php:79
msgid "Enter your brands here"
msgstr ""

#: inc/vendors/elementor/widgets/brands.php:130
#: inc/vendors/elementor/widgets/posts.php:155
#: inc/vendors/elementor/widgets/testimonials.php:129
msgid "Show Nav"
msgstr ""

#: inc/vendors/elementor/widgets/call_to_action.php:16
msgid "Apus Call To Action"
msgstr ""

#: inc/vendors/elementor/widgets/call_to_action.php:64
#: inc/vendors/elementor/widgets/nav_menu.php:89
msgid "Button Link"
msgstr ""

#: inc/vendors/elementor/widgets/call_to_action.php:85
msgid "Theme "
msgstr ""

#: inc/vendors/elementor/widgets/call_to_action.php:86
msgid "Yellow "
msgstr ""

#: inc/vendors/elementor/widgets/call_to_action.php:109
#: inc/vendors/elementor/widgets/posts.php:205
msgid "Tyles"
msgstr ""

#: inc/vendors/elementor/widgets/call_to_action.php:159
#: inc/vendors/elementor/widgets/mailchimp.php:193
#: inc/vendors/elementor/widgets/mailchimp.php:236
msgid "Button Color"
msgstr ""

#: inc/vendors/elementor/widgets/call_to_action.php:172
#: inc/vendors/elementor/widgets/mailchimp.php:205
#: inc/vendors/elementor/widgets/mailchimp.php:249
msgid "Button Background"
msgstr ""

#: inc/vendors/elementor/widgets/call_to_action.php:185
msgid "Button Typography"
msgstr ""

#: inc/vendors/elementor/widgets/countdown.php:16
msgid "Apus Countdown"
msgstr ""

#: inc/vendors/elementor/widgets/countdown.php:28
msgid "Countdown"
msgstr ""

#: inc/vendors/elementor/widgets/countdown.php:35
msgid "End Date"
msgstr ""

#: inc/vendors/elementor/widgets/features_box.php:16
msgid "Apus Features Box"
msgstr ""

#: inc/vendors/elementor/widgets/features_box.php:32
#: inc/vendors/elementor/widgets/features_box.php:128
msgid "Features Box"
msgstr ""

#: inc/vendors/elementor/widgets/features_box.php:95
#: inc/vendors/elementor/widgets/list_icon.php:95
msgid "Title & Description"
msgstr ""

#: inc/vendors/elementor/widgets/features_box.php:356
msgid "Heading Color Hover"
msgstr ""

#: inc/vendors/elementor/widgets/features_box.php:367
msgid "Heading Typography"
msgstr ""

#: inc/vendors/elementor/widgets/features_box.php:434
msgid "Hover Color"
msgstr ""

#: inc/vendors/elementor/widgets/heading.php:42
msgid "Apus Heading"
msgstr ""

#: inc/vendors/elementor/widgets/heading.php:114
msgid "Add Your Heading Text Here"
msgstr ""

#: inc/vendors/elementor/widgets/heading.php:122
msgid "Line Decor Image"
msgstr ""

#: inc/vendors/elementor/widgets/heading.php:124
msgid "Decor Background Image"
msgstr ""

#: inc/vendors/elementor/widgets/heading.php:151
msgid "Small"
msgstr ""

#: inc/vendors/elementor/widgets/heading.php:152
msgid "Medium"
msgstr ""

#: inc/vendors/elementor/widgets/heading.php:153
msgid "Large"
msgstr ""

#: inc/vendors/elementor/widgets/heading.php:154
msgid "XL"
msgstr ""

#: inc/vendors/elementor/widgets/heading.php:155
msgid "XXL"
msgstr ""

#: inc/vendors/elementor/widgets/heading.php:163
msgid "HTML Tag"
msgstr ""

#: inc/vendors/elementor/widgets/heading.php:213
#: woocommerce/myaccount/my-orders.php:83
msgid "View"
msgstr ""

#: inc/vendors/elementor/widgets/heading.php:265
msgid "Blend Mode"
msgstr ""

#: inc/vendors/elementor/widgets/list_icon.php:16
msgid "Apus List Icon"
msgstr ""

#: inc/vendors/elementor/widgets/list_icon.php:32
#: inc/vendors/elementor/widgets/list_icon.php:126
msgid "List Icon"
msgstr ""

#: inc/vendors/elementor/widgets/list_icon.php:208
msgid "Margin Title"
msgstr ""

#: inc/vendors/elementor/widgets/mailchimp.php:16
msgid "Apus MailChimp Sign-Up Form"
msgstr ""

#: inc/vendors/elementor/widgets/mailchimp.php:28
msgid "MailChimp Sign-Up Form"
msgstr ""

#: inc/vendors/elementor/widgets/mailchimp.php:39
msgid "Show Text"
msgstr ""

#: inc/vendors/elementor/widgets/mailchimp.php:40
msgid "Show Icon"
msgstr ""

#: inc/vendors/elementor/widgets/mailchimp.php:60
msgid "From"
msgstr ""

#: inc/vendors/elementor/widgets/mailchimp.php:68
msgid "Background"
msgstr ""

#: inc/vendors/elementor/widgets/mailchimp.php:104
msgid "Input"
msgstr ""

#: inc/vendors/elementor/widgets/mailchimp.php:112
msgid "Input Color"
msgstr ""

#: inc/vendors/elementor/widgets/mailchimp.php:125
msgid "Input Placeholder Color"
msgstr ""

#: inc/vendors/elementor/widgets/mailchimp.php:141
msgid "Input Background"
msgstr ""

#: inc/vendors/elementor/widgets/mailchimp.php:154
msgid "Padding Input"
msgstr ""

#: inc/vendors/elementor/widgets/mailchimp.php:166
msgid "Typography Input"
msgstr ""

#: inc/vendors/elementor/widgets/mailchimp.php:216
#: inc/vendors/elementor/widgets/mailchimp.php:261
msgid "Button Border"
msgstr ""

#: inc/vendors/elementor/widgets/nav_menu.php:16
msgid "Apus Navigation Menu"
msgstr ""

#: inc/vendors/elementor/widgets/nav_menu.php:38
msgid "Navigation Menu"
msgstr ""

#: inc/vendors/elementor/widgets/nav_menu.php:69
#: inc/vendors/elementor/widgets/social_links.php:186
msgid "Circle"
msgstr ""

#: inc/vendors/elementor/widgets/nav_menu.php:174
msgid "Menu Item"
msgstr ""

#: inc/vendors/elementor/widgets/nav_menu.php:182
msgid "Menu Color"
msgstr ""

#: inc/vendors/elementor/widgets/nav_menu.php:193
msgid "Menu Color Hover"
msgstr ""

#: inc/vendors/elementor/widgets/nav_menu.php:208
msgid "Menu Typography"
msgstr ""

#: inc/vendors/elementor/widgets/popup_video.php:16
msgid "Apus Popup Video"
msgstr ""

#: inc/vendors/elementor/widgets/popup_video.php:49
msgid "Youtube Video Link"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:14
msgid "Apus Posts"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:26
msgid "Posts"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:47
msgid "Number posts to display"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:55
msgid "Order by"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:61
msgid "Author"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:63
msgid "Modified"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:64
msgid "Random"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:65
msgid "Comment count"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:75
msgid "Sort order"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:79
msgid "Ascending"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:80
msgid "Descending"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:89
msgid "Item Style"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:112
msgid "Special"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:234
msgid "Post Title Color"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:246
msgid "Post Title Typography"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:255
msgid "Post Excerpt Color"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:267
msgid "Post Excerpt Typography"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:276
msgid "Post Tag Color"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:288
msgid "Post Tag Typography"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:297
msgid "Post Read More Color"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:309
msgid "Post Read More Typography"
msgstr ""

#: inc/vendors/elementor/widgets/revslider.php:14
msgid "Apus Slider Revolution"
msgstr ""

#: inc/vendors/elementor/widgets/revslider.php:33
msgid "No sliders found"
msgstr ""

#: inc/vendors/elementor/widgets/revslider.php:38
msgid "Revslider"
msgstr ""

#: inc/vendors/elementor/widgets/revslider.php:48
msgid "Select your Revolution Slider."
msgstr ""

#: inc/vendors/elementor/widgets/social_links.php:16
msgid "Apus Social Links"
msgstr ""

#: inc/vendors/elementor/widgets/social_links.php:49
#: inc/vendors/elementor/widgets/social_links.php:51
msgid "Social Title"
msgstr ""

#: inc/vendors/elementor/widgets/social_links.php:153
msgid "Social Icons"
msgstr ""

#: inc/vendors/elementor/widgets/social_links.php:238
msgid "Social"
msgstr ""

#: inc/vendors/elementor/widgets/tabs.php:16
msgid "Apus Tabs"
msgstr ""

#: inc/vendors/elementor/widgets/tabs.php:78
msgid "Content Type"
msgstr ""

#: inc/vendors/elementor/widgets/tabs.php:82
msgid "Template"
msgstr ""

#: inc/vendors/elementor/widgets/tabs.php:83
msgid "Editor"
msgstr ""

#: inc/vendors/elementor/widgets/tabs.php:109
msgid "Tab Item Content"
msgstr ""

#: inc/vendors/elementor/widgets/tabs.php:124
msgid "Enter title here"
msgstr ""

#: inc/vendors/elementor/widgets/team.php:16
msgid "Apus Teams"
msgstr ""

#: inc/vendors/elementor/widgets/team.php:32
msgid "Team"
msgstr ""

#: inc/vendors/elementor/widgets/team.php:42
msgid "Social Icon"
msgstr ""

#: inc/vendors/elementor/widgets/team.php:120
msgid "Link Social"
msgstr ""

#: inc/vendors/elementor/widgets/team.php:135
#: inc/vendors/elementor/widgets/team.php:137
msgid "Member Name"
msgstr ""

#: inc/vendors/elementor/widgets/team.php:144
#: inc/vendors/elementor/widgets/team.php:146
msgid "Member Listing"
msgstr ""

#: inc/vendors/elementor/widgets/team.php:164
msgid "Socials"
msgstr ""

#: inc/vendors/elementor/widgets/team.php:194
msgid "Background Hover Color"
msgstr ""

#: inc/vendors/elementor/widgets/testimonials.php:16
msgid "Apus Testimonials"
msgstr ""

#: inc/vendors/elementor/widgets/testimonials.php:78
#: inc/vendors/simple-event/functions.php:163
msgid "Job"
msgstr ""

#: inc/vendors/elementor/widgets/testimonials.php:87
msgid "Star Scores"
msgstr ""

#: inc/vendors/elementor/widgets/testimonials.php:96
msgid "Link To"
msgstr ""

#: inc/vendors/elementor/widgets/testimonials.php:98
msgid "Enter your social link here"
msgstr ""

#: inc/vendors/elementor/widgets/testimonials.php:106
msgid "Testimonials"
msgstr ""

#: inc/vendors/elementor/widgets/testimonials.php:190
msgid "Style Box"
msgstr ""

#: inc/vendors/elementor/widgets/testimonials.php:199
msgid "Border Box"
msgstr ""

#: inc/vendors/elementor/widgets/testimonials.php:209
msgid "Style Info"
msgstr ""

#: inc/vendors/elementor/widgets/testimonials.php:237
msgid "Name Color"
msgstr ""

#: inc/vendors/elementor/widgets/testimonials.php:249
msgid "Name Typography"
msgstr ""

#: inc/vendors/elementor/widgets/testimonials.php:258
msgid "Content Color"
msgstr ""

#: inc/vendors/elementor/widgets/testimonials.php:269
msgid "Content Typography"
msgstr ""

#: inc/vendors/elementor/widgets/testimonials.php:278
msgid "Listing Color"
msgstr ""

#: inc/vendors/elementor/widgets/testimonials.php:289
msgid "Listing Typography"
msgstr ""

#: inc/vendors/elementor/widgets/testimonials.php:299
msgid "Border Image Active"
msgstr ""

#: inc/vendors/elementor/widgets/testimonials.php:309
msgid "Style Dots"
msgstr ""

#: inc/vendors/elementor/widgets/testimonials.php:328
msgid "Color Active"
msgstr ""

#: inc/vendors/elementor/woo_widgets/woo_header_cart.php:16
msgid "Apus Header WooCoomerce Cart"
msgstr ""

#: inc/vendors/elementor/woo_widgets/woo_header_cart.php:68
msgid "Icon Hover Color"
msgstr ""

#: inc/vendors/elementor/woo_widgets/woo_header_cart.php:81
msgid "Color Count"
msgstr ""

#: inc/vendors/elementor/woo_widgets/woo_header_cart.php:93
msgid "Bg Count"
msgstr ""

#: inc/vendors/elementor/woo_widgets/woo_header_cart.php:124
#: inc/vendors/elementor/woo_widgets/woo_header_cart.php:143
#: woocommerce/cart/mini-cart-button.php:4
msgid "View your shopping cart"
msgstr ""

#: inc/vendors/elementor/woo_widgets/woo_products.php:16
msgid "Apus Products"
msgstr ""

#: inc/vendors/elementor/woo_widgets/woo_products.php:39
msgid "Widget Title"
msgstr ""

#: inc/vendors/elementor/woo_widgets/woo_products.php:47
msgid "Get Products By"
msgstr ""

#: inc/vendors/elementor/woo_widgets/woo_products.php:50
msgid "Recent Products"
msgstr ""

#: inc/vendors/elementor/woo_widgets/woo_products.php:51
msgid "Best Selling"
msgstr ""

#: inc/vendors/elementor/woo_widgets/woo_products.php:52
msgid "Featured Products"
msgstr ""

#: inc/vendors/elementor/woo_widgets/woo_products.php:53
msgid "Top Rate"
msgstr ""

#: inc/vendors/elementor/woo_widgets/woo_products.php:54
msgid "On Sale"
msgstr ""

#: inc/vendors/elementor/woo_widgets/woo_products.php:67
msgid "Enter slug spearate by comma(,)"
msgstr ""

#: inc/vendors/elementor/woo_widgets/woo_products.php:223
msgid "Widget Title Color"
msgstr ""

#: inc/vendors/elementor/woo_widgets/woo_products.php:235
msgid "Widget Title Typography"
msgstr ""

#: inc/vendors/elementor/woo_widgets/woo_products.php:253
msgid "Border Hover Color"
msgstr ""

#: inc/vendors/elementor/woo_widgets/woo_products.php:268
msgid "Box Shadow Hover"
msgstr ""

#: inc/vendors/elementor/woo_widgets/woo_products.php:279
msgid "Product Style"
msgstr ""

#: inc/vendors/elementor/woo_widgets/woo_products.php:321
msgid "Category Color"
msgstr ""

#: inc/vendors/elementor/woo_widgets/woo_products.php:333
msgid "Category Typography"
msgstr ""

#: inc/vendors/elementor/woo_widgets/woo_products.php:355
msgid "Price Old Color"
msgstr ""

#: inc/vendors/elementor/woo_widgets/woo_products.php:367
msgid "Price Typography"
msgstr ""

#: inc/vendors/learnpress/customizer.php:24
msgid "LearnPress Courses Settings"
msgstr ""

#: inc/vendors/learnpress/customizer.php:83
#: inc/vendors/tutor/customizer.php:83
msgid "Courses Archives"
msgstr ""

#: inc/vendors/learnpress/customizer.php:96
#: inc/vendors/learnpress/customizer.php:270
#: inc/vendors/simple-event/customizer.php:108
#: inc/vendors/simple-event/customizer.php:273
#: inc/vendors/tutor/customizer.php:96
#: inc/vendors/tutor/customizer.php:240
#: inc/vendors/woocommerce/customizer.php:96
#: inc/vendors/woocommerce/customizer.php:285
msgid "General Settings"
msgstr ""

#: inc/vendors/learnpress/customizer.php:144
#: inc/vendors/tutor/customizer.php:144
msgid "Select the variation you want to apply on your course/archive page."
msgstr ""

#: inc/vendors/learnpress/customizer.php:185
#: inc/vendors/tutor/customizer.php:155
msgid "Courses Filter Layout"
msgstr ""

#: inc/vendors/learnpress/customizer.php:189
#: inc/vendors/tutor/customizer.php:159
msgid "None"
msgstr ""

#: inc/vendors/learnpress/customizer.php:190
#: inc/vendors/tutor/customizer.php:160
msgid "Filter Top"
msgstr ""

#: inc/vendors/learnpress/customizer.php:191
#: inc/vendors/tutor/customizer.php:161
msgid "Filter Offcanvas"
msgstr ""

#: inc/vendors/learnpress/customizer.php:203
#: inc/vendors/tutor/customizer.php:173
msgid "Courses Layout"
msgstr ""

#: inc/vendors/learnpress/customizer.php:220
#: inc/vendors/tutor/customizer.php:190
msgid "Courses Grid Item"
msgstr ""

#: inc/vendors/learnpress/customizer.php:244
#: inc/vendors/tutor/customizer.php:214
msgid "Courses Columns"
msgstr ""

#: inc/vendors/learnpress/customizer.php:257
#: inc/vendors/tutor/customizer.php:227
msgid "Single Course"
msgstr ""

#: inc/vendors/learnpress/customizer.php:283
#: inc/vendors/tutor/customizer.php:253
msgid "Course Layout"
msgstr ""

#: inc/vendors/learnpress/customizer.php:287
#: inc/vendors/learnpress/functions.php:408
#: inc/vendors/tutor/customizer.php:257
#: inc/vendors/tutor/functions.php:141
msgid "Layout 1"
msgstr ""

#: inc/vendors/learnpress/customizer.php:288
#: inc/vendors/learnpress/functions.php:409
#: inc/vendors/tutor/customizer.php:258
#: inc/vendors/tutor/functions.php:142
msgid "Layout 2"
msgstr ""

#: inc/vendors/learnpress/customizer.php:289
#: inc/vendors/learnpress/functions.php:410
#: inc/vendors/tutor/customizer.php:259
#: inc/vendors/tutor/functions.php:143
msgid "Layout 3"
msgstr ""

#: inc/vendors/learnpress/customizer.php:290
#: inc/vendors/learnpress/functions.php:411
#: inc/vendors/tutor/customizer.php:260
#: inc/vendors/tutor/functions.php:144
msgid "Layout 4"
msgstr ""

#: inc/vendors/learnpress/customizer.php:291
#: inc/vendors/learnpress/functions.php:412
#: inc/vendors/tutor/customizer.php:261
#: inc/vendors/tutor/functions.php:145
msgid "Layout 5"
msgstr ""

#: inc/vendors/learnpress/customizer.php:292
#: inc/vendors/learnpress/functions.php:413
#: inc/vendors/tutor/customizer.php:262
#: inc/vendors/tutor/functions.php:146
msgid "Layout 6"
msgstr ""

#: inc/vendors/learnpress/customizer.php:322
#: inc/vendors/tutor/customizer.php:292
msgid "Show Course Review Tab"
msgstr ""

#: inc/vendors/learnpress/customizer.php:335
#: inc/vendors/tutor/customizer.php:305
msgid "Course Block Settings"
msgstr ""

#: inc/vendors/learnpress/customizer.php:350
#: inc/vendors/tutor/customizer.php:320
msgid "Show Courses Related"
msgstr ""

#: inc/vendors/learnpress/customizer.php:362
#: inc/vendors/tutor/customizer.php:332
msgid "Number related courses"
msgstr ""

#: inc/vendors/learnpress/customizer.php:375
#: inc/vendors/tutor/customizer.php:345
msgid "Related Courses Columns"
msgstr ""

#: inc/vendors/learnpress/functions-review.php:181
#: inc/vendors/tutor/functions-review.php:31
msgid "(%d)"
msgid_plural "(%d)"
msgstr[0] ""
msgstr[1] ""

#: inc/vendors/learnpress/functions-wishlist.php:30
#: inc/vendors/learnpress/functions-wishlist.php:129
msgid "Added to wishlist"
msgstr ""

#: inc/vendors/learnpress/functions-wishlist.php:60
#: inc/vendors/learnpress/functions-wishlist.php:123
msgid "Add to wishlist"
msgstr ""

#: inc/vendors/learnpress/functions.php:369
#: learnpress/single-course/header-6.php:112
#: widgets/course-info-theme.php:74
msgid "Language"
msgstr ""

#: inc/vendors/learnpress/functions.php:370
msgid "The language of the course."
msgstr ""

#: inc/vendors/learnpress/functions.php:376
#: learnpress/single-course/header-6.php:120
#: widgets/course-info-theme.php:82
msgid "Certificate"
msgstr ""

#: inc/vendors/learnpress/functions.php:377
msgid "Set certificate course."
msgstr ""

#: inc/vendors/learnpress/functions.php:391
msgid "Video url"
msgstr ""

#: inc/vendors/learnpress/functions.php:394
msgid "Enter youtube or vimeo video."
msgstr ""

#: inc/vendors/learnpress/functions.php:397
#: inc/vendors/learnpress/functions.php:420
#: inc/vendors/tutor/functions.php:153
msgid "More Information"
msgstr ""

#: inc/vendors/learnpress/functions.php:407
#: inc/vendors/tutor/functions.php:140
msgid "Global Settings"
msgstr ""

#: inc/vendors/learnpress/functions.php:526
#: learnpress/single-course/reviews.php:74
#: learnpress/single-course/reviews.php:119
#: learnpress/single-course/reviews.php:180
#: tutor/single/course/reviews.php:107
msgid "Reviews"
msgstr ""

#: inc/vendors/learnpress/functions.php:599
msgid "<span>%d</span> course found"
msgid_plural "<span>%d</span> courses found"
msgstr[0] ""
msgstr[1] ""

#: inc/vendors/learnpress/functions.php:606
#: inc/vendors/simple-event/functions.php:241
msgid "Newest"
msgstr ""

#: inc/vendors/learnpress/functions.php:607
#: inc/vendors/simple-event/functions.php:242
msgid "Oldest"
msgstr ""

#: inc/vendors/learnpress/functions.php:614
#: inc/vendors/simple-event/functions.php:249
msgid "Sort By:"
msgstr ""

#: inc/vendors/one-click-demo-import/functions.php:16
#: inc/vendors/one-click-demo-import/functions.php:27
msgid "Import process may take 5-10 minutes. If you facing any issues please contact our support."
msgstr ""

#: inc/vendors/simple-event/customizer.php:24
msgid "Events Settings"
msgstr ""

#: inc/vendors/simple-event/customizer.php:87
msgid "Google Map API Key"
msgstr ""

#: inc/vendors/simple-event/customizer.php:95
msgid "Events Archives"
msgstr ""

#: inc/vendors/simple-event/customizer.php:156
#: inc/vendors/simple-event/customizer.php:322
msgid "Select the variation you want to apply on your event/archive page."
msgstr ""

#: inc/vendors/simple-event/customizer.php:197
msgid "Events Layout"
msgstr ""

#: inc/vendors/simple-event/customizer.php:214
msgid "Event Item Style"
msgstr ""

#: inc/vendors/simple-event/customizer.php:219
msgid "V1"
msgstr ""

#: inc/vendors/simple-event/customizer.php:220
msgid "V2"
msgstr ""

#: inc/vendors/simple-event/customizer.php:233
msgid "Events Columns"
msgstr ""

#: inc/vendors/simple-event/customizer.php:247
msgid "Number of Events Per Page"
msgstr ""

#: inc/vendors/simple-event/customizer.php:260
msgid "Single Event"
msgstr ""

#: inc/vendors/simple-event/functions.php:137
msgid "Speakers {#}"
msgstr ""

#: inc/vendors/simple-event/functions.php:138
msgid "Add Another Speakers"
msgstr ""

#: inc/vendors/simple-event/functions.php:139
msgid "Remove Speakers"
msgstr ""

#: inc/vendors/simple-event/functions.php:160
msgid "e.g. John Doe"
msgstr ""

#: inc/vendors/simple-event/functions.php:166
msgid "e.g. Web Designer"
msgstr ""

#: inc/vendors/simple-event/functions.php:177
msgid "Select product"
msgstr ""

#: inc/vendors/simple-event/functions.php:181
msgid "(When selling the event) Sell your product, process by WooCommerce"
msgstr ""

#. translators: %d: total results
#: inc/vendors/simple-event/functions.php:225
msgid "Showing the single result"
msgid_plural "Showing all %d results"
msgstr[0] ""
msgstr[1] ""

#. translators: 1: first result 2: last result 3: total results
#: inc/vendors/simple-event/functions.php:230
msgctxt "with first and last result"
msgid "Showing the single result"
msgid_plural "Showing <span class=\"first\">%1$d</span> &ndash; <span class=\"last\">%2$d</span> of %3$d results"
msgstr[0] ""
msgstr[1] ""

#: inc/vendors/tutor/customizer.php:24
msgid "Tutor Courses Settings"
msgstr ""

#: inc/vendors/tutor/functions.php:172
#: learnpress/single-course/tabs/instructor.php:24
msgid "Instructor"
msgstr ""

#: inc/vendors/woocommerce/customizer.php:24
msgid "Shop Settings"
msgstr ""

#: inc/vendors/woocommerce/customizer.php:83
msgid "Product Archives"
msgstr ""

#: inc/vendors/woocommerce/customizer.php:111
msgid "Show Shop/Category Title ?"
msgstr ""

#: inc/vendors/woocommerce/customizer.php:140
msgid "Product Columns"
msgstr ""

#: inc/vendors/woocommerce/customizer.php:154
msgid "Number of Products Per Page"
msgstr ""

#: inc/vendors/woocommerce/customizer.php:170
msgid "Enable Swap Image"
msgstr ""

#: inc/vendors/woocommerce/customizer.php:183
#: inc/vendors/woocommerce/customizer.php:359
msgid "Sidebar Settings"
msgstr ""

#: inc/vendors/woocommerce/customizer.php:217
msgid "Select the variation you want to apply on your shop/archive page."
msgstr ""

#: inc/vendors/woocommerce/customizer.php:272
msgid "Single Product"
msgstr ""

#: inc/vendors/woocommerce/customizer.php:297
msgid "Thumbnails Position"
msgstr ""

#: inc/vendors/woocommerce/customizer.php:301
msgid "Thumbnails Left"
msgstr ""

#: inc/vendors/woocommerce/customizer.php:302
msgid "Thumbnails Right"
msgstr ""

#: inc/vendors/woocommerce/customizer.php:303
msgid "Thumbnails Bottom"
msgstr ""

#: inc/vendors/woocommerce/customizer.php:315
msgid "Number Thumbnails Per Row"
msgstr ""

#: inc/vendors/woocommerce/customizer.php:346
msgid "Show Product Review Tab"
msgstr ""

#: inc/vendors/woocommerce/customizer.php:404
msgid "Single Left Sidebar"
msgstr ""

#: inc/vendors/woocommerce/customizer.php:419
msgid "Single Right Sidebar"
msgstr ""

#: inc/vendors/woocommerce/customizer.php:435
msgid "Product Block Settings"
msgstr ""

#: inc/vendors/woocommerce/customizer.php:450
msgid "Show Products Related"
msgstr ""

#: inc/vendors/woocommerce/customizer.php:462
msgid "Number related products"
msgstr ""

#: inc/vendors/woocommerce/customizer.php:475
msgid "Related Products Columns"
msgstr ""

#: inc/vendors/woocommerce/customizer.php:492
msgid "Show Products upsells"
msgstr ""

#: inc/vendors/woocommerce/customizer.php:504
msgid "Upsells Products Columns"
msgstr ""

#: inc/vendors/woocommerce/functions.php:106
#: woocommerce/single-product/short-description.php:35
msgid "View More"
msgstr ""

#: inc/vendors/woocommerce/functions.php:107
msgid "View Less"
msgstr ""

#: inc/vendors/woocommerce/functions.php:290
#: inc/vendors/woocommerce/functions.php:330
msgid "Placeholder"
msgstr ""

#: inc/widgets/course-info.php:7
msgid "LearnPress Single Course:: Information"
msgstr ""

#: inc/widgets/course-info.php:8
msgid "Show list of course information"
msgstr ""

#: inc/widgets/course-info.php:25
#: inc/widgets/custom_menu.php:40
#: inc/widgets/event-product.php:25
#: inc/widgets/events-categories.php:25
#: inc/widgets/events-list.php:31
#: inc/widgets/events-tags.php:25
#: inc/widgets/filter-category.php:28
#: inc/widgets/filter-instructor.php:28
#: inc/widgets/filter-keywords.php:25
#: inc/widgets/filter-level.php:28
#: inc/widgets/filter-price.php:28
#: inc/widgets/filter-rating.php:28
#: inc/widgets/recent_post.php:28
#: inc/widgets/search.php:26
#: inc/widgets/socials.php:37
msgid "Title:"
msgstr ""

#: inc/widgets/custom_menu.php:7
msgid "Apus Custom Menu Widget"
msgstr ""

#: inc/widgets/custom_menu.php:8
msgid "Show custom menu"
msgstr ""

#: inc/widgets/custom_menu.php:45
msgid "Menu:"
msgstr ""

#: inc/widgets/custom_menu.php:57
#: inc/widgets/socials.php:70
msgid "Style:"
msgstr ""

#: inc/widgets/elementor-template.php:7
msgid "Apus Show Elementor Template Widget"
msgstr ""

#: inc/widgets/elementor-template.php:8
msgid "Show Elementor Template"
msgstr ""

#: inc/widgets/elementor-template.php:35
msgid "Choose Template:"
msgstr ""

#: inc/widgets/event-product.php:7
msgid "Event Single:: WooCoomerce Product"
msgstr ""

#: inc/widgets/event-product.php:8
msgid "Show WooCoomerce product"
msgstr ""

#: inc/widgets/events-categories.php:7
msgid "Events Categories"
msgstr ""

#: inc/widgets/events-categories.php:8
msgid "Show list of events categories list"
msgstr ""

#: inc/widgets/events-list.php:7
msgid "Events List"
msgstr ""

#: inc/widgets/events-list.php:8
msgid "Show list of events list"
msgstr ""

#: inc/widgets/events-list.php:36
msgid "Get Events By:"
msgstr ""

#: inc/widgets/events-list.php:46
msgid "Limit:"
msgstr ""

#: inc/widgets/events-tags.php:7
msgid "Events Tags"
msgstr ""

#: inc/widgets/events-tags.php:8
msgid "Show list of events tags"
msgstr ""

#: inc/widgets/filter-category.php:7
msgid "LearnPress Courses:: Filter Categories"
msgstr ""

#: inc/widgets/filter-category.php:8
msgid "Show list of course filter category"
msgstr ""

#: inc/widgets/filter-category.php:34
#: inc/widgets/filter-instructor.php:33
#: inc/widgets/filter-level.php:33
#: inc/widgets/filter-price.php:34
#: inc/widgets/filter-rating.php:34
msgid "Layout:"
msgstr ""

#: inc/widgets/filter-instructor.php:7
msgid "LearnPress Courses:: Filter Instructors"
msgstr ""

#: inc/widgets/filter-instructor.php:8
msgid "Show list of course filter instructor"
msgstr ""

#: inc/widgets/filter-keywords.php:7
msgid "LearnPress Courses:: Filter Keywords"
msgstr ""

#: inc/widgets/filter-keywords.php:8
msgid "Show list of course filter keywords"
msgstr ""

#: inc/widgets/filter-level.php:7
msgid "LearnPress Courses:: Filter Levels"
msgstr ""

#: inc/widgets/filter-level.php:8
msgid "Show list of course filter level"
msgstr ""

#: inc/widgets/filter-price.php:7
msgid "LearnPress Courses:: Filter Prices"
msgstr ""

#: inc/widgets/filter-price.php:8
msgid "Show list of course filter price"
msgstr ""

#: inc/widgets/filter-rating.php:7
msgid "LearnPress Courses:: Filter Ratings"
msgstr ""

#: inc/widgets/filter-rating.php:8
msgid "Show list of course filter rating"
msgstr ""

#: inc/widgets/recent_post.php:7
msgid "Apus Recent Posts Widget"
msgstr ""

#: inc/widgets/recent_post.php:8
msgid "Show list of recent post"
msgstr ""

#: inc/widgets/recent_post.php:33
#: inc/widgets/search.php:31
msgid "Type:"
msgstr ""

#: inc/widgets/recent_post.php:45
msgid "Num Posts:"
msgstr ""

#: inc/widgets/search.php:7
msgid "Apus Search Widget"
msgstr ""

#: inc/widgets/search.php:8
msgid "Show search form in sidebar"
msgstr ""

#: inc/widgets/socials.php:7
msgid "Apus Socials"
msgstr ""

#: inc/widgets/socials.php:8
msgid "Socials for website."
msgstr ""

#: inc/widgets/socials.php:41
msgid "Select socials:"
msgstr ""

#: learnpress/addons/co-instructors/single-course-tab.php:55
#: learnpress/single-course/tabs/instructor.php:69
msgid "%d Review"
msgid_plural "%d Reviews"
msgstr[0] ""
msgstr[1] ""

#: learnpress/addons/co-instructors/single-course-tab.php:59
#: learnpress/content-instructor.php:70
#: learnpress/single-course/tabs/instructor.php:73
msgid "%d Student"
msgid_plural "%d Students"
msgstr[0] ""
msgstr[1] ""

#: learnpress/addons/co-instructors/single-course-tab.php:63
#: learnpress/content-instructor.php:74
#: learnpress/single-course/tabs/instructor.php:77
msgid "%d Course"
msgid_plural "%d Courses"
msgstr[0] ""
msgstr[1] ""

#: learnpress/content-course-2.php:37
#: learnpress/content-course-3.php:37
#: learnpress/content-course-4.php:37
#: learnpress/content-course-list-v2.php:37
#: learnpress/content-course-list-v3.php:37
#: learnpress/content-course-list-v4.php:37
#: learnpress/content-course-list-v5.php:37
#: learnpress/content-course-list.php:37
#: learnpress/content-course-wishlist.php:37
#: learnpress/content-course.php:37
#: tutor/loop/course/course-2.php:42
#: tutor/loop/course/course-3.php:42
#: tutor/loop/course/course-4.php:42
#: tutor/loop/course/course-list-v2.php:42
#: tutor/loop/course/course-list-v3.php:42
#: tutor/loop/course/course-list-v4.php:42
#: tutor/loop/course/course-list-v5.php:42
#: tutor/loop/course/course-list.php:43
#: tutor/loop/course/course.php:44
msgid "Sale"
msgstr ""

#: learnpress/content-course-2.php:72
#: learnpress/content-course-3.php:72
#: learnpress/content-course-4.php:72
#: learnpress/content-course-list-v2.php:71
#: learnpress/content-course-list-v3.php:81
#: learnpress/content-course-list-v4.php:81
#: learnpress/content-course-list-v5.php:81
#: learnpress/content-course-list.php:71
#: learnpress/content-course-wishlist.php:71
#: learnpress/content-course.php:72
#: learnpress/single-course/header-6.php:96
#: tutor/loop/course/course-2.php:79
#: tutor/loop/course/course-3.php:79
#: tutor/loop/course/course-4.php:79
#: tutor/loop/course/course-list-v2.php:79
#: tutor/loop/course/course-list-v3.php:88
#: tutor/loop/course/course-list-v4.php:89
#: tutor/loop/course/course-list-v5.php:89
#: tutor/loop/course/course-list.php:80
#: tutor/loop/course/course.php:81
#: widgets/course-info-theme.php:58
msgid "Lessons"
msgstr ""

#: learnpress/content-course-2.php:86
#: learnpress/content-course-3.php:86
#: learnpress/content-course-4.php:86
#: learnpress/content-course-list-v2.php:85
#: learnpress/content-course-list-v3.php:95
#: learnpress/content-course-list-v4.php:95
#: learnpress/content-course-list-v5.php:95
#: learnpress/content-course-list.php:85
#: learnpress/content-course-wishlist.php:85
#: learnpress/content-course.php:86
msgid "All Levels"
msgstr ""

#: learnpress/content-instructor-v2.php:55
#: tutor/instructor/instructor-v2.php:72
msgid "View Profile"
msgstr ""

#: learnpress/courses-top-bar.php:30
#: tutor/course-filter/course-archive-filter-bar.php:48
#: tutor/course-filter/course-archive-filter-bar.php:51
msgid "Filter"
msgstr ""

#: learnpress/pages/profile.php:44
#: woocommerce/myaccount/form-login.php:32
msgid "Login"
msgstr ""

#: learnpress/pages/profile.php:49
#: woocommerce/myaccount/form-login.php:67
#: woocommerce/myaccount/form-login.php:101
msgid "Register"
msgstr ""

#: learnpress/pages/profile.php:78
msgid "This user does not public their profile."
msgstr ""

#: learnpress/single-course/courses-related.php:38
#: tutor/single/course/courses-related.php:39
msgid "Related Courses"
msgstr ""

#: learnpress/single-course/header-3.php:58
#: learnpress/single-course/header-4.php:58
#: learnpress/single-course/header-5.php:58
#: learnpress/single-course/header-6.php:58
#: learnpress/single-course/header.php:57
#: tutor/single/course/header-3.php:58
#: tutor/single/course/header-4.php:59
#: tutor/single/course/header-5.php:59
#: tutor/single/course/header-6.php:59
#: tutor/single/course/header.php:58
msgid "Enrolled"
msgstr ""

#: learnpress/single-course/header-6.php:91
#: tutor/single/course/course-entry-box.php:27
#: tutor/single/course/header-6.php:97
#: widgets/course-info-theme.php:53
msgid "Duration"
msgstr ""

#: learnpress/single-course/header-6.php:101
#: widgets/course-info-theme.php:63
msgid "Quizzes"
msgstr ""

#: learnpress/single-course/header-6.php:107
#: widgets/course-info-theme.php:69
msgid "Maximum Students"
msgstr ""

#: learnpress/single-course/header-6.php:116
#: widgets/course-info-theme.php:78
msgid "Skill level"
msgstr ""

#: learnpress/single-course/loop-section.php:47
msgctxt "template title empty"
msgid "Untitled"
msgstr ""

#: learnpress/single-course/loop-section.php:67
msgid "Section progress %s%%"
msgstr ""

#: learnpress/single-course/loop-section.php:79
msgid "No items in this section"
msgstr ""

#: learnpress/single-course/review.php:25
#: woocommerce/single-product/review.php:32
msgid "Your comment is awaiting approval"
msgstr ""

#: learnpress/single-course/review.php:33
#: woocommerce/single-product/review.php:47
msgid "Rated %d out of 5"
msgstr ""

#: learnpress/single-course/reviews.php:19
#: tutor/single/course/reviews.php:48
msgid "Feedback"
msgstr ""

#: learnpress/single-course/reviews.php:27
msgid "%1$s rating"
msgid_plural "%1$s ratings"
msgstr[0] ""
msgstr[1] ""

#: learnpress/single-course/reviews.php:27
msgid "0 rating"
msgstr ""

#: learnpress/single-course/reviews.php:74
#: tutor/single/course/reviews.php:107
#: woocommerce/single-product-reviews.php:111
msgid "Review"
msgstr ""

#: learnpress/single-course/reviews.php:95
msgid "Reply comment"
msgstr ""

#: learnpress/single-course/reviews.php:96
#: learnpress/single-course/reviews.php:130
#: woocommerce/single-product-reviews.php:73
msgid "Leave a Reply to %s"
msgstr ""

#: learnpress/single-course/reviews.php:105
#: learnpress/single-course/reviews.php:139
msgid "<EMAIL>"
msgstr ""

#: learnpress/single-course/reviews.php:107
#: learnpress/single-course/reviews.php:141
msgid "Your Website"
msgstr ""

#: learnpress/single-course/reviews.php:110
msgid "Submit"
msgstr ""

#: learnpress/single-course/reviews.php:119
#: learnpress/single-course/reviews.php:180
msgid "Write Reviews"
msgstr ""

#: learnpress/single-course/reviews.php:121
msgid "You must be logged in to reply this review."
msgstr ""

#: learnpress/single-course/reviews.php:129
#: woocommerce/single-product-reviews.php:72
msgid "Add a review"
msgstr ""

#: learnpress/single-course/reviews.php:129
#: woocommerce/single-product-reviews.php:72
msgid "Be the first to review &ldquo;%s&rdquo;"
msgstr ""

#: learnpress/single-course/reviews.php:144
#: tutor/single/course/reviews.php:169
msgid "Submit Review"
msgstr ""

#: learnpress/single-course/reviews.php:154
msgid "You must be logged in to post a review."
msgstr ""

#: learnpress/single-course/reviews.php:159
msgid "What is it like to Course?"
msgstr ""

#: learnpress/single-course/tabs/curriculum.php:25
msgid "Curriculum"
msgstr ""

#: learnpress/single-course/tabs/curriculum.php:57
msgid "Curriculum is empty"
msgstr ""

#: learnpress/single-course/tabs/overview.php:25
msgid "Course Overview"
msgstr ""

#: learnpress/single-course/tabs/tabs.php:36
msgid "You finished this course. This course has been blocked"
msgstr ""

#: learnpress/single-course/tabs/tabs.php:41
msgid "This course has been blocked reason by expire"
msgstr ""

#. Template Name of the theme
msgid "Sidebar Dark Template"
msgstr ""

#: page-sidebar-dark.php:49
#: page-sidebar.php:49
#: page.php:46
#: template-posts/single/inner.php:37
msgid "Pages:"
msgstr ""

#: page-sidebar-dark.php:53
#: page-sidebar.php:53
#: page.php:50
#: template-posts/single/inner.php:41
msgid "Page"
msgstr ""

#. Template Name of the theme
msgid "Sidebar Template"
msgstr ""

#: searchform.php:12
#: tutor/course-filter/filters.php:21
#: widgets/search.php:18
msgid "Search..."
msgstr ""

#: template-parts/posts-related.php:34
msgid "Related Posts"
msgstr ""

#: template-parts/sharebox-course.php:9
#: template-parts/sharebox.php:12
msgid "Share on facebook"
msgstr ""

#: template-parts/sharebox-course.php:15
#: template-parts/sharebox.php:18
msgid "Share on Twitter"
msgstr ""

#: template-parts/sharebox-course.php:22
#: template-parts/sharebox.php:25
msgid "Share on LinkedIn"
msgstr ""

#: template-parts/sharebox-course.php:30
#: template-parts/sharebox.php:33
msgid "Share on Pinterest"
msgstr ""

#: template-parts/sharebox.php:7
msgid "Share Post :"
msgstr ""

#: template-posts/content-none.php:13
msgid "Nothing Found"
msgstr ""

#: template-posts/content-none.php:15
msgid "Try again please, use the search form below."
msgstr ""

#: template-posts/single/inner.php:72
msgid "Prev"
msgstr ""

#: templates-event/loop/inner-list.php:38
#: templates-event/loop/inner.php:35
msgid "More"
msgstr ""

#: templates-event/single-simple_event.php:65
msgid "Our Speakers"
msgstr ""

#: tutor/course-filter/course-archive-filter-bar.php:28
msgid "%s Courses"
msgstr ""

#: tutor/course-filter/course-archive-filter-bar.php:28
msgid "%s Course"
msgstr ""

#: tutor/course-filter/course-archive-filter-bar.php:36
msgid "Release Date (newest first)"
msgstr ""

#: tutor/course-filter/course-archive-filter-bar.php:38
msgid "Release Date (oldest first)"
msgstr ""

#: tutor/course-filter/course-archive-filter-bar.php:39
msgid "Course Title (a-z)"
msgstr ""

#: tutor/course-filter/course-archive-filter-bar.php:40
msgid "Course Title (z-a)"
msgstr ""

#: tutor/course-filter/filters.php:5
#: tutor/single/course/course-entry-box-2.php:180
#: tutor/single/course/course-entry-box.php:207
#: widgets/filter-price.php:64
#: widgets/filter-price.php:128
msgid "Free"
msgstr ""

#: tutor/course-filter/filters.php:6
#: widgets/filter-price.php:81
#: widgets/filter-price.php:147
msgid "Paid"
msgstr ""

#: tutor/course-filter/filters.php:43
#: tutor/single/course/tags.php:20
msgid "Tags"
msgstr ""

#: tutor/course-filter/filters.php:57
msgid "Levels"
msgstr ""

#: tutor/course-filter/filters.php:87
msgid "Prices"
msgstr ""

#: tutor/course-filter/filters.php:108
msgid "Clear All Filters"
msgstr ""

#: tutor/instructor/default.php:86
#: tutor/public-profile.php:113
#: tutor/single/course/instructors.php:59
msgid "Students"
msgstr ""

#: tutor/instructor/default.php:86
#: tutor/public-profile.php:113
#: tutor/single/course/instructors.php:59
msgid "Student"
msgstr ""

#: tutor/instructor/default.php:91
#: tutor/public-profile.php:107
#: tutor/single/course/instructors.php:64
msgid "Course"
msgstr ""

#: tutor/loop/course/course-list-v3.php:133
#: tutor/single/course/course-entry-box-2.php:216
#: tutor/single/course/course-entry-box.php:243
msgid "Wishlist"
msgstr ""

#: tutor/profile/courses_taken.php:40
msgid "No course yet."
msgstr ""

#: tutor/public-profile.php:126
msgid "Courses Enrolled"
msgstr ""

#: tutor/public-profile.php:126
msgid "Course Enrolled"
msgstr ""

#: tutor/public-profile.php:132
msgid "Courses Completed"
msgstr ""

#: tutor/public-profile.php:132
msgid "Course Completed"
msgstr ""

#: tutor/public-profile.php:162
msgid "Biography"
msgstr ""

#: tutor/single/course/add-to-cart-woocommerce.php:23
#: widgets/event-product.php:49
#: woocommerce/cart/mini-cart.php:98
msgid "View Cart"
msgstr ""

#: tutor/single/course/add-to-cart-woocommerce.php:55
#: widgets/event-product.php:67
msgid "Please make sure that your product exists and valid for this course"
msgstr ""

#: tutor/single/course/course-benefits.php:28
msgid "What Will You Learn?"
msgstr ""

#: tutor/single/course/course-content.php:34
msgid "About Course"
msgstr ""

#: tutor/single/course/course-content.php:43
msgid "Show More"
msgstr ""

#: tutor/single/course/course-entry-box-2.php:48
#: tutor/single/course/course-entry-box.php:75
msgid "Course Progress"
msgstr ""

#: tutor/single/course/course-entry-box-2.php:58
#: tutor/single/course/course-entry-box.php:85
msgid "Complete"
msgstr ""

#: tutor/single/course/course-entry-box-2.php:88
#: tutor/single/course/course-entry-box.php:115
msgid "Retake This Course"
msgstr ""

#: tutor/single/course/course-entry-box-2.php:90
#: tutor/single/course/course-entry-box-2.php:147
#: tutor/single/course/course-entry-box.php:117
#: tutor/single/course/course-entry-box.php:174
msgid "Start Learning"
msgstr ""

#: tutor/single/course/course-entry-box-2.php:92
#: tutor/single/course/course-entry-box.php:119
msgid "Continue Learning"
msgstr ""

#: tutor/single/course/course-entry-box-2.php:112
#: tutor/single/course/course-entry-box.php:139
msgid "Complete Course"
msgstr ""

#: tutor/single/course/course-entry-box-2.php:128
#: tutor/single/course/course-entry-box.php:155
msgid "You enrolled in this course on"
msgstr ""

#: tutor/single/course/course-entry-box-2.php:162
#: tutor/single/course/course-entry-box.php:189
msgid "This course is full right now. We limit the number of students to create an optimized and productive group dynamic."
msgstr ""

#: tutor/single/course/course-entry-box-2.php:190
#: tutor/single/course/course-entry-box.php:217
msgid "Enroll now"
msgstr ""

#: tutor/single/course/course-entry-box.php:22
#: tutor/single/course/header-6.php:92
msgid "Total Enrolled"
msgstr ""

#: tutor/single/course/course-entry-box.php:32
#: tutor/single/course/header-6.php:102
msgid "Last Updated"
msgstr ""

#: tutor/single/course/course-entry-box.php:41
#: tutor/single/course/header-6.php:111
msgid "Level"
msgstr ""

#: tutor/single/course/course-topics.php:28
msgid "Course Content"
msgstr ""

#: tutor/single/course/course-topics.php:96
msgid "Expired"
msgstr ""

#: tutor/single/course/course-topics.php:98
msgid "Live"
msgstr ""

#: tutor/single/course/reviews-loop.php:17
msgid "%s ago"
msgstr ""

#: tutor/single/course/reviews-loop.php:21
msgid "Pending"
msgstr ""

#: tutor/single/course/reviews.php:45
msgid "No Review Yet"
msgstr ""

#: tutor/single/course/reviews.php:59
msgid "Total "
msgstr ""

#: tutor/single/course/reviews.php:61
msgid " Rating"
msgid_plural " Ratings"
msgstr[0] ""
msgstr[1] ""

#: tutor/single/course/reviews.php:121
msgid "Write a review"
msgstr ""

#: tutor/single/course/reviews.php:121
msgid "Edit review"
msgstr ""

#: tutor/single/course/reviews.php:135
msgid "Load More"
msgstr ""

#: tutor/single/course/reviews.php:165
msgid "write a review"
msgstr ""

#: widgets/event-product.php:56
msgid "Book Now"
msgstr ""

#: widgets/filter-category.php:42
msgid "All Categories"
msgstr ""

#: widgets/filter-instructor.php:36
msgid "All Instructors"
msgstr ""

#: widgets/filter-keywords.php:26
msgid "Search courses..."
msgstr ""

#: widgets/filter-price.php:35
#: widgets/filter-price.php:97
msgid "All"
msgstr ""

#: widgets/filter-rating.php:25
msgid "All Ratings"
msgstr ""

#: woocommerce/cart/cart.php:30
msgid "Product Name"
msgstr ""

#: woocommerce/cart/cart.php:31
#: woocommerce/cart/cart.php:83
msgid "Price"
msgstr ""

#: woocommerce/cart/cart.php:32
#: woocommerce/cart/cart.php:89
#: woocommerce/global/quantity-input.php:31
msgid "Quantity"
msgstr ""

#: woocommerce/cart/cart.php:33
#: woocommerce/cart/cart.php:111
#: woocommerce/cart/mini-cart.php:93
#: woocommerce/checkout/thankyou.php:54
#: woocommerce/myaccount/my-orders.php:37
#: woocommerce/myaccount/my-orders.php:60
#: woocommerce/order/order-details.php:55
msgid "Total"
msgstr ""

#: woocommerce/cart/cart.php:34
msgid "Remove"
msgstr ""

#: woocommerce/cart/cart.php:63
#: woocommerce/order/order-details.php:54
msgid "Product"
msgstr ""

#: woocommerce/cart/cart.php:78
msgid "Available on backorder"
msgstr ""

#: woocommerce/cart/cart.php:122
#: woocommerce/cart/mini-cart.php:67
msgid "Remove this item"
msgstr ""

#: woocommerce/cart/cart.php:141
msgid "Coupon:"
msgstr ""

#: woocommerce/cart/cart.php:141
msgid "Coupon code"
msgstr ""

#: woocommerce/cart/cart.php:141
msgid "Apply coupon"
msgstr ""

#: woocommerce/cart/cart.php:146
msgid "Update cart"
msgstr ""

#: woocommerce/cart/cross-sells.php:23
msgid "You may be interested in&hellip;"
msgstr ""

#: woocommerce/cart/mini-cart-content.php:5
msgid "My Cart"
msgstr ""

#: woocommerce/cart/mini-cart.php:84
msgid "Currently Empty"
msgstr ""

#: woocommerce/cart/mini-cart.php:86
msgid "Continue shopping"
msgstr ""

#: woocommerce/cart/mini-cart.php:99
msgid "Checkout"
msgstr ""

#: woocommerce/cart/proceed-to-checkout-button.php:26
msgid "Proceed to Checkout"
msgstr ""

#: woocommerce/checkout/form-billing.php:25
msgid "Billing &amp; Shipping"
msgstr ""

#: woocommerce/checkout/form-billing.php:29
msgid "Billing details"
msgstr ""

#: woocommerce/checkout/form-billing.php:54
msgid "Create an account?"
msgstr ""

#: woocommerce/checkout/form-checkout.php:21
msgid "You must be logged in to checkout."
msgstr ""

#: woocommerce/checkout/form-checkout.php:47
msgid "Your order"
msgstr ""

#: woocommerce/checkout/form-shipping.php:26
msgid "Ship to a different address?"
msgstr ""

#: woocommerce/checkout/form-shipping.php:56
msgid "Additional Information"
msgstr ""

#: woocommerce/checkout/payment.php:35
msgid "Sorry, it seems that there are no available payment methods for your state. Please contact us if you require assistance or wish to make alternate arrangements."
msgstr ""

#: woocommerce/checkout/payment.php:35
msgid "Please fill in your details above to see available payment methods."
msgstr ""

#. translators: $1 and $2 opening and closing emphasis tags respectively
#: woocommerce/checkout/payment.php:44
msgid "Since your browser does not support JavaScript, or it is disabled, please ensure you click the %1$sUpdate Totals%2$s button before placing your order. You may be charged more than the amount stated above if you fail to do so."
msgstr ""

#: woocommerce/checkout/payment.php:46
msgid "Update totals"
msgstr ""

#: woocommerce/checkout/thankyou.php:25
msgid "Unfortunately your order cannot be processed as the originating bank/merchant has declined your transaction. Please attempt your purchase again."
msgstr ""

#: woocommerce/checkout/thankyou.php:28
#: woocommerce/myaccount/my-orders.php:70
msgid "Pay"
msgstr ""

#: woocommerce/checkout/thankyou.php:30
msgid "My Account"
msgstr ""

#: woocommerce/checkout/thankyou.php:37
msgid "Your order is completed!"
msgstr ""

#: woocommerce/checkout/thankyou.php:39
#: woocommerce/checkout/thankyou.php:74
msgid "Thank you. Your order has been received."
msgstr ""

#: woocommerce/checkout/thankyou.php:46
#: woocommerce/myaccount/my-orders.php:49
msgid "Order Number"
msgstr ""

#: woocommerce/checkout/thankyou.php:59
msgid "Payment Method"
msgstr ""

#: woocommerce/loop/orderby.php:24
msgid "Sort by:"
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:21
msgid "First name"
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:25
msgid "Last name"
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:30
msgid "Display name"
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:31
msgid "This will be how your name will be displayed in the account section and in reviews"
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:34
#: woocommerce/myaccount/form-login.php:81
msgid "Email address"
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:38
msgid "Password Change"
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:40
msgid "Current Password (leave blank to leave unchanged)"
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:44
msgid "New Password (leave blank to leave unchanged)"
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:48
msgid "Confirm New Password"
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:56
msgid "Save changes"
msgstr ""

#: woocommerce/myaccount/form-login.php:38
msgid "Username or email address"
msgstr ""

#: woocommerce/myaccount/form-login.php:41
#: woocommerce/myaccount/form-login.php:87
msgid "Password"
msgstr ""

#: woocommerce/myaccount/form-login.php:50
msgid "Remember me"
msgstr ""

#: woocommerce/myaccount/form-login.php:53
msgid "Lost your password?"
msgstr ""

#: woocommerce/myaccount/form-login.php:56
msgid "sign in"
msgstr ""

#: woocommerce/myaccount/form-login.php:75
msgid "Username"
msgstr ""

#: woocommerce/myaccount/form-login.php:92
msgid "A password will be sent to your email address."
msgstr ""

#: woocommerce/myaccount/form-lost-password.php:25
msgid "Lost your password? Please enter your username or email address. You will receive a link to create a new password via email."
msgstr ""

#: woocommerce/myaccount/form-lost-password.php:28
msgid "Username or email"
msgstr ""

#: woocommerce/myaccount/form-lost-password.php:38
msgid "Reset password"
msgstr ""

#: woocommerce/myaccount/my-address.php:17
msgid "My Addresses"
msgstr ""

#: woocommerce/myaccount/my-address.php:19
#: woocommerce/myaccount/my-address.php:25
msgid "Billing Address"
msgstr ""

#: woocommerce/myaccount/my-address.php:20
msgid "Shipping Address"
msgstr ""

#: woocommerce/myaccount/my-address.php:23
msgid "My Address"
msgstr ""

#: woocommerce/myaccount/my-address.php:35
msgid "The following addresses will be used on the checkout page by default."
msgstr ""

#: woocommerce/myaccount/my-address.php:67
msgid "You have not set up this type of address yet."
msgstr ""

#: woocommerce/myaccount/my-orders.php:26
msgid "Recent Orders"
msgstr ""

#: woocommerce/myaccount/my-orders.php:61
msgid "%s for %s item"
msgid_plural "%s for %s items"
msgstr[0] ""
msgstr[1] ""

#: woocommerce/myaccount/my-orders.php:77
msgid "Cancel"
msgstr ""

#: woocommerce/order/order-details.php:48
msgid "Order details"
msgstr ""

#: woocommerce/order/order-details.php:96
msgid "Note:"
msgstr ""

#: woocommerce/single-product-reviews.php:47
msgid "There are no reviews yet."
msgstr ""

#: woocommerce/single-product-reviews.php:57
#: woocommerce/single-product-reviews.php:100
msgid "Your Rating"
msgstr ""

#: woocommerce/single-product-reviews.php:59
#: woocommerce/single-product-reviews.php:101
msgid "Rate&hellip;"
msgstr ""

#: woocommerce/single-product-reviews.php:60
#: woocommerce/single-product-reviews.php:102
msgid "Perfect"
msgstr ""

#: woocommerce/single-product-reviews.php:61
#: woocommerce/single-product-reviews.php:103
msgid "Good"
msgstr ""

#: woocommerce/single-product-reviews.php:62
#: woocommerce/single-product-reviews.php:104
msgid "Average"
msgstr ""

#: woocommerce/single-product-reviews.php:63
#: woocommerce/single-product-reviews.php:105
msgid "Not that bad"
msgstr ""

#: woocommerce/single-product-reviews.php:64
#: woocommerce/single-product-reviews.php:106
msgid "Very Poor"
msgstr ""

#: woocommerce/single-product-reviews.php:89
msgid "submit review"
msgstr ""

#: woocommerce/single-product-reviews.php:96
msgid "You must be <a href=\"%s\">logged in</a> to post a review."
msgstr ""

#: woocommerce/single-product-reviews.php:112
msgid "Your Review"
msgstr ""

#: woocommerce/single-product-reviews.php:122
msgid "Only logged in customers who have purchased this product may leave a review."
msgstr ""

#: woocommerce/single-product/countdown.php:9
msgid "Start for you in: "
msgstr ""

#: woocommerce/single-product/meta.php:35
msgid "SKU:"
msgstr ""

#: woocommerce/single-product/meta.php:35
msgid "N/A"
msgstr ""

#: woocommerce/single-product/meta.php:39
msgid "Category:"
msgid_plural "Categories:"
msgstr[0] ""
msgstr[1] ""

#: woocommerce/single-product/meta.php:41
msgid "<span class=\"sub_title\">Tag:</span>"
msgid_plural "<span class=\"sub_title\">Tags:</span>"
msgstr[0] ""
msgstr[1] ""

#: woocommerce/single-product/product-image-carousel.php:42
#: woocommerce/single-product/product-image.php:42
msgid "Watch video"
msgstr ""

#: woocommerce/single-product/product-image-carousel.php:56
#: woocommerce/single-product/product-image-carousel.php:83
#: woocommerce/single-product/product-image.php:56
#: woocommerce/single-product/product-image.php:77
msgid "Awaiting product image"
msgstr ""

#: woocommerce/single-product/related.php:45
msgid "Related Products"
msgstr ""

#: woocommerce/single-product/review.php:38
msgid "verified owner"
msgstr ""

#: woocommerce/single-product/review.php:48
msgid "out of 5"
msgstr ""

#: woocommerce/single-product/tabs/description.php:22
msgid "Details"
msgstr ""

#: woocommerce/single-product/up-sells.php:42
msgid "Up-Sells Products"
msgstr ""
