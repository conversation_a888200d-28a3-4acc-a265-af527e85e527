/*-------------------------------------------
    Style for woocommerce
-------------------------------------------*/
.woocommerce div.product .stock{
    color: $body-color;
    &:before{
        display: inline-block;
        @include size(18px,18px);
        text-align: center;
        line-height: 18px;
        @include border-radius(50%);
        color: #fff;
        font-size: 8px;
        background-color: $theme-color;
        margin-right: 5px;
        content: "\f00c";
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        vertical-align: middle;
    }
}
.woo-pay-perfect {
    font-size: 16px;
    font-weight: 600;
    margin-top: $theme-margin;
    p:last-child{
        margin-bottom: 0;
    }
}
.update_cart{
    opacity: 1 !important;
    filter: alpha(opacity=100) !important;
}
.woocommerce-shipping-destination{
    color: $body-link;
    strong{
        font-weight: 400;
    }
}
.woocommerce-shipping-methods{
    font-size: $font-size-base;
    label{
        color: $body-link;
        font-weight: 400 !important;
    }
}
.woocommerce form .form-row-first, .woocommerce form .form-row-last, 
.woocommerce-page form .form-row-first, .woocommerce-page form .form-row-last{
    width: calc(50% - 15px);
}
.woocommerce form .form-row-last,
.woocommerce-page form .form-row-last{
    float: right;
    margin-left: 30px;
}
.woocommerce form.checkout_coupon, .woocommerce form.login, .woocommerce form.register{
    background-color: #fff;
    border:1px solid $border-color;
    @include border-radius($border-radius);
}
.woocommerce a.added_to_cart,
.woocommerce input.button:disabled, .woocommerce input.button:disabled[disabled],
.woocommerce #respond input#submit.alt, .woocommerce a.button.alt, .woocommerce button.button.alt, .woocommerce input.button.alt,
.woocommerce #respond input#submit, .woocommerce input.button, .woocommerce button.button,.woocommerce a.button{
      line-height: $line-height-base;
      font-weight: 500;
      text-align:center;
      display: inline-block;
      @include border-radius($border-radius);
      white-space: nowrap;
      font-size: 15px;
      @include transition(all 0.3s ease-in-out 0s);
      border: 2px solid $theme-color;
      padding: 0.6rem 1.875rem !important;
      background: $theme-color;
      color: #fff;
      text-transform: capitalize;
      &:hover,&:focus{
        background: #fff;
        border-color:$theme-color;
        color: $theme-color;;
      }
}
.woocommerce ul.order_details{
    margin: 0 0 2em;
}
.woocommerce #review_form #respond{
    .form-submit{
        margin:0;
    }
    #commentform{
        margin: 0;
    }
    .comment-form-rating{
        margin: 0 0 1rem;
    }
}
.woocommerce #review_form{
    margin: 0;
}
.pp_gallery ul{
    height: auto;
    a{
        height: auto;
    }
}
.woocommerce table.shop_attributes{
    border:0;
    margin:0;
    th{
        font-weight: 500;
        color: $body-link;
        width:25%;
        background-color:transparent !important;
        border-bottom: 1px solid $border-color;
        border-width: 0 0 1px;
        padding:12px 0;
    }
    td{
        padding:12px 0;
        background-color:transparent !important;
        border-bottom: 1px solid $border-color;
        border-width: 0 0 1px;
        font-style: normal;
        p{
            padding:0;
        }
    }
}
.woocommerce .woocommerce-customer-details address{
    border:0;
    padding:0;
    font-size: 15px;
    font-weight: 500;
}
.woocommerce div.product form.cart .variations select{
    height: 40px;
}
.woocommerce #respond input#submit.loading, .woocommerce a.button.loading, .woocommerce button.button.loading, .woocommerce input.button.loading{
    @include opacity(1);
    padding-right:$btn-padding-x;
    &:after{
        top: 50%;
        right: 0;
        color:$theme-color;
        margin:0;
        z-index:9;
        width:100%;
        height: 1rem;
        line-height: 1rem;
        margin-top: -0.5rem;
    }
    &:before{
        @include opacity(0.9);
        z-index:8;
        position:absolute;
        top:-2px;
        left:-2px;
        background:#fff;
        @include size(calc(100% + 4px),calc(100% + 4px));
        content:'';
        @include border-radius($border-radius);
    }
}
.woocommerce div.product div.images .woocommerce-product-gallery__trigger{
    border:1px solid $theme-color;
    background:$theme-color;
    @include transition(all 0.2s ease-in-out 0s);
    &:hover,&:active{
        background: var(--educrat-theme-hover-color);
    }
    &:before{
        border-color:#fff;
    }
    &:after{
        background:#fff;
    }
}
@media(min-width:1200px){
    .woocommerce div.product div.images .flex-control-thumbs li{
        width: 33.33%;
        &:nth-child(3n + 1){
            clear: left;
        }
    }
}
.woocommerce div.product div.images .flex-control-thumbs{
    margin-left: -10px;
    margin-right: -10px;
    margin-top: 20px;
    li{
        padding-right:10px;
        padding-left:10px;
        margin-bottom: 20px;
        img{
            border:1px solid #fff;
            @include opacity(0.8);
            @include transition(all 0.2s ease-in-out 0s);
            &:hover,
            &:active,
            &.flex-active{
                border-color:$theme-color;
            }
        }
    }
}
.shop-pagination{
    .apus-pagination{
        margin:0;
        float: left;
    }
    .woocommerce-result-count{
        float: right;
        margin:5px 0 0; 
    }
}
.woocommerce div.product form.cart .variations{
    margin-bottom: 0;
}
table.variations{
    .tawcvs-swatches .swatch-color{
        @include opacity(1);
        @include size(24px,24px);
        line-height: 24px;
        position:relative;
        border:none;
        margin-right: 15px;
        &:before{
            display:none !important;
        }
        &:after{
            content:'';
            @include border-radius(50%);
            z-index:2;
            position:absolute;
            top:-1px;
            left:-1px;
            @include size(26px,26px);
            border:5px solid #fff;
        }
        &.selected{
            @include box-shadow(none);
            &:after{
                top:1px;
                left:1px;
                @include size(22px,22px);
                border:3px solid #fff;
            }
        }
    }
    .tawcvs-swatches .swatch-label{
        font-size:12px;
        font-weight:400;
        color:$body-color;
        padding:9px;
        display:inline-block;
        line-height:1;
        background:#f2f3f5;
        min-width:30px;
        text-align:center;
        height:auto;
        width:auto;
        border:none !important;
        @include border-radius(50%);
        margin-right: 10px;
        text-transform:uppercase;
        @include opacity(1);
        &.selected{
            @include box-shadow(none);
            background:$theme-color;
            color:#fff;
        }
    }
    tr:last-child{

    }
}
.woocommerce div.product form.cart .variations td.label{
    padding:10px 0;
    text-align: inherit;
    display: table-cell;
    vertical-align: middle;
    label{
        margin:0;
    }
}
.woocommerce div.product form.cart.swatches-support .variations td.label{
    vertical-align:top;
}
.woocommerce div.product form.cart .reset_variations{
    color: $danger; 
    i{
        font-size: 12px;
        margin-right: 3px;
        color: #e23e1d;
    }
}
.woocommerce #respond input#submit.added:after, 
.woocommerce a.button.added:after, 
.woocommerce button.button.added:after, 
.woocommerce input.button.added:after{
    display: none;
}
.woocommerce form .form-row input.input-text, .woocommerce form .form-row textarea{
    line-height: $line-height-base;
    border:1px solid $input-border-color;
    background:#fff;
    @include transition(all 0.2s ease-in-out 0s);
    @include box-shadow(none !important);
    @include border-radius($border-radius);
    height: $input-height;
    &:focus{
        border-color: $input-focus-border-color;
    }
}
.woocommerce form .form-row {
    textarea{
        padding: 20px;
        height: 100px;
        @media(min-width: 1200px){
            height: 200px;
        }
    }
}
.woocommerce table.wishlist_table thead th{
    padding:10px 0;
    color:$body-link;
    border-bottom:1px solid $border-color;
    @media(min-width:992px){
        padding:20px 0;
    }
}
.woocommerce table.wishlist_table tbody td{
    padding:10px 0;
    text-align: inherit;
    border-width:0 0 1px;
    border-bottom:1px solid $border-color;
    @media(min-width:992px){
        padding:20px 0;
    }
}
.woocommerce table.wishlist_table tfoot td {
    border:none;
}
.woocommerce table.wishlist_table{
    font-size:$font-size-base;
    .product-name{
        white-space: nowrap;
        padding-right:20px;
        padding-left:20px;
        @media(min-width:992px){
            padding-right:50px;
            padding-left:50px;
        }
    }
    .media-body{
        width:auto;
    }
    .product-thumbnail{
        a{
            display: block;
            width: 80px;
            @media(min-width: 1200px){
                width:170px;
            }
        }
    }
}
.track_order{
    max-width:770px;
    margin:auto;
    padding:15px;
    background:#f2f3f5;
    @media(min-width:992px){
        padding:70px;
    }
    .form-row{
        width:100% !important;
        input.input-text{
            padding:5px 20px;
            background:#fff !important;
            height:$input-height;
        }
        &:last-child{
            margin-bottom:0;
        }
        label{
            color:$body-link;
        }
    }
}
.woocommerce-error,
.woocommerce-message{
    line-height: 3.4;
    background-color: #fff;
    border-color: $theme-color;
    @include box-shadow(0 4px 10px 0 rgba(#000,0.05));
}
#add_payment_method #payment ul.payment_methods, .woocommerce-cart #payment ul.payment_methods, .woocommerce-checkout #payment ul.payment_methods{
    border:0;
    padding:0;
    li{
        margin:0 0 12px;
        .payment_box{
            padding: 5px 28px;
            margin:0;
            font-size: $font-size-base;
            background: transparent;
            color: $body-color;
        }
        label{
            cursor: pointer;
            font-weight: 500;
            font-family: $headings-font-family;
            display: inline;
            color: $body-link;
        }
        [for="payment_method_paypal"] img{
            display: none;
        }
        .about_paypal{
            float:none;
            line-height: inherit;
        }
    }
}
#add_payment_method #payment ul.payment_methods li input, .woocommerce-cart #payment ul.payment_methods li input, .woocommerce-checkout #payment ul.payment_methods li input{
    margin-right: 10px;
}
.woocommerce table.shop_table{
    border:1px solid $border-color;
    @include border-radius($border-radius);
    margin: 0;
    th{
        color: inherit;
        font-size: 1rem;
        font-weight: 500;
        padding:10px;
        color: $theme-color;
        @media(min-width:1200px){
            padding:25px 15px;
        }
        border:0;
        &:last-child{
            text-align: right;
        }
    }
    td{
        border:none;
        border-bottom:1px solid $border-color;
        overflow: hidden;
        padding:10px;
        background-color: transparent !important;
        @media(min-width:1200px){
            padding:25px 15px;
        }
        &.product-thumbnail{
            width: 100px;
            @media(min-width: 1200px){
                width: 140px;
            }
            a{
                display: block;
                width: 100%;
                @include border-radius($border-radius);
                overflow: hidden;
            }
        }
        &:last-child{
            text-align: right;
        }
    }
    @media(min-width: 1200px){
        th,td{
            &:first-child{
                padding-left: 30px;
            }
            &:last-child{
                padding-right: 30px;
            }
        }
    }
    thead{
        background-color: #F5F7FE;
    }
    .reader-text{
        display: none;
    }
    .quantity-wrapper{
        > label{
            display: none;
        }
    }
    .product-remove{
        .remove{
            background-color: transparent !important;
            font-size: 1rem;
            font-weight: 400;
            padding:12px;
            @include size(auto,auto);
            margin:0;
            color:$danger;
            &:hover,&:active{
                color:$danger;
            }
        }
    }
    tbody{
        tr{
            &:last-child{
                background-color: transparent;
            }
        }
        .actions{
            padding: 1rem;
            border:0 !important;
            @media(min-width: 1200px){
                padding: 1.875rem;
            }
        }
        .product-subtotal{
            font-weight: 500;
            font-size: 1rem;
            color: $body-link;
        }
        .product-name{
            font-size: 1rem;
        }
        .cart-subtotal,
        .order-total{
            .woocommerce-Price-amount{
                color: $body-link;
            }
        }
    }
    .list-bundles{
        font-size:14px;
        list-style:none;
        padding-left: 25px;
        strong{
            font-weight:500;
        }
        ul{
            list-style:inside none disc;
            padding:0;
            margin:0;
        }
    }
}
.woocommerce .cart_totals{
    > h2{
        font-weight: 500;
        margin:0 0 10px;
        font-size: 20px;
        @media(min-width:1200px){
            margin:0 0 20px;
        }
    }
    table.shop_table{
        border:none;
        margin:0;
        th,td{
            padding:10px 0;
            border-top: 1px dashed #dadde8;
            border-bottom: 0;
            @media(min-width:1200px){
                padding:15px 0;
            }
        }
        th{
            font-weight: 500;
            font-size: 15px;
            color: $body-link;
        }
        strong{
            font-weight: 500;
        }
        [data-title="Total"],
        [data-title="Subtotal"]{
            font-weight: 500;
            font-size: 1rem;
        }
    }
    .wc-proceed-to-checkout{
        padding:15px 0 0;
    }
}
.cart-collaterals{
    background:#F7F8FB;
    padding:1.25rem;
    @include border-radius($border-radius);
    border: 1px solid #EDEDED;
    margin-top: 20px;
    margin-bottom: 20px;
    @media(min-width: 768px){
        margin-top: 50px;
        margin-bottom: 50px;
    }
    @media(min-width: 1200px){
        padding:$theme-margin;
        margin-top: 80px;
        margin-bottom: 80px;
    }
}
#add_payment_method #payment, .woocommerce-cart #payment, .woocommerce-checkout #payment{
    background: transparent;
    .place-order{
        padding:10px 0 0 !important;
        margin-bottom: 0;
        #place_order{
            width: 100%;
        }
    }
}
.woocommerce-checkout #payment{
    @include border-radius(0);
    padding-top: 20px;
    border-top: 1px solid $border-color;
}
#add_payment_method #payment div.payment_box::before, .woocommerce-cart #payment div.payment_box::before, .woocommerce-checkout #payment div.payment_box::before{
    display: none;
}
.woocommerce  #customer_details{
    h3.form-row{
        font-size: 18px;
        font-weight: 400;
        text-transform: capitalize;
        margin: 0; 
        padding:20px 0;
    }
}
.woocommerce form .woocommerce-billing-fields,
.woocommerce form .woocommerce-shipping-fields{
    > h3{
        font-size: 18px;
        font-weight: 500;
        @media(min-width: 1200px){
            font-size: 20px;
        }
        margin: 0 0 20px;
        input[type="checkbox"]{
            position:static;
            margin: -3px 5px 0 0;
            vertical-align: middle;
        }
    }
    .select2-container{
        height:$input-height;
    }
    .woocommerce-billing-fields__field-wrapper{
        > *{
            > .select2-container,
            > select,
            > input{
                overflow: hidden;
                width: calc(100% - 200px) !important;
                border-width:0 0 1px;
                border-style:solid;
                border-color:$border-color;
                padding:10px 0;
                @include border-radius(0 !important);
                float: right;
                &:focus{
                    border-color:$theme-color;
                }
            }
            > .select2-hidden-accessible{
                height: 0;
            }
        }
    }
}
.woocommerce form .woocommerce-shipping-fields{
    margin-top: 1rem;
    @media(min-width: 1200px){
        margin-top: 2rem;
    }
    > h3{
        margin-bottom: 10px;
    }
}
form.woocommerce-checkout{
    margin-bottom: $theme-margin;
}
.woocommerce form .form-row label, 
.woocommerce-page form .form-row label{
    margin-bottom: 5px;
}
.woocommerce .cart-collaterals .cross-sells, .woocommerce-page .cart-collaterals .cross-sells,
.woocommerce .cart-collaterals .cart_totals, .woocommerce-page .cart-collaterals .cart_totals{
    width: 100%;
}
.woocommerce div.product .product_title{
    font-size: 25px;
    margin:0 0 5px;
    @media(min-width: 1200px){
        font-size: 30px;
    }
}
.woocommerce div.product p.price, .woocommerce div.product span.price{
    ins,
    &{
        color: $theme-color;
        font-size: 1rem;
        font-weight: 500;
    }
    del{
        font-size: 14px;
        color: $body-color;
    }
}
.woocommerce div.product p.price del, .woocommerce div.product span.price del{
    @include opacity(1);
}
.variations{
    label{
        color: $body-color;
        font-size: 15px;
        text-transform: capitalize;
        font-weight: 400 !important;
        padding-right: 5px;
    }
    .value{
        padding: 0;
    }
}
.woocommerce div.product form.cart .group_table{
    border:none;
    margin-bottom: 10px;
    .price del{
        font-size: 12px !important;
    }
    .price,
    .price ins{
        font-size: 15px !important;
        color: $theme-color;
    }
    label{
        font-weight:500;
    }
    td{
        vertical-align: middle;
        &:first-child{
            padding-right:0;
            text-align: left;
        }
    }
    .quantity{
        .reader-text{
            display:none;
        }
    }
}
.woocommerce div.product form.cart.group_product{
    width:100%;
}
.woocommerce div.product form.cart .group_table .label{
    padding: 0.5em;
    vertical-align: middle;
    font-size:14px;
    display: table-cell;
    text-align: inherit;
    white-space: normal;
    label{
        font-weight: 400;
    }
}
.woocommerce div.product form.cart .variations td{
    line-height: inherit;
    font-size: inherit;
    .tawcvs-swatches{
        padding:0;
    }
    padding:10px 0;
    vertical-align: middle;
}
.woocommerce .order_details{
    padding: 0;
}
.woocommerce #content table.cart td.actions .coupon, 
.woocommerce table.cart td.actions .coupon, 
.woocommerce-page #content table.cart td.actions .coupon, 
.woocommerce-page table.cart td.actions .coupon{
    border:1px solid $input-border-color;
    @include border-radius($border-radius);
    @include flexbox();
    align-items: center;
    .input-text{
        height:$input-height;
        padding:5px 20px !important;
        border: 0;
        width: auto;
        margin-right: 15px;
        &::-webkit-input-placeholder { /* Edge */
          @include opacity(1);
        }
        &:-ms-input-placeholder { /* Internet Explorer 10-11 */
          @include opacity(1);
        }
        &::placeholder {
          @include opacity(1);
        }
    }
    .btn{
        font-size: 14px;
        color: $theme-color;
        padding: 10px 20px;
        &:hover,&:focus{
            color: $theme-hover-color;
        }
    }
}
#add_payment_method table.cart img, .woocommerce-cart table.cart img, .woocommerce-checkout table.cart img{
    width: 90px;
    @include border-radius($border-radius);
}
.woocommerce .percent-sale,
.woocommerce span.onsale{
    color:#fff;
    font-size: 14px;
    background:#ff5a5f;
    padding:0 20px;
    position: absolute;
    text-align: center;
    left:10px;
    text-transform:capitalize;
    top: 10px;
    min-height: auto;
    z-index: 1;
    @include border-radius(3px);
    display: inline-block;
    line-height: 2;
    font-weight: 400;
}
//popup-cart
.popup-cart{
    .title-count,
    .title-add{
        font-size: 20px;
        margin: 0 0 20px;
    }
    .gr-buttons{
        margin: 50px 0 0;
    }
    .title-add{
        color: $success;
    }
    .image{
        img{
            max-width: 100px;
        }
    }
    .name{
         margin: 30px 0 0;
    }
    .widget-product{
        margin-top: 30px;
    }
}
#apus-cart-modal{
    .btn-close{
        position: absolute;
        top:0;
        right: 0;
        z-index: 99;
        background: #fff;
        @include size(30px,30px);
        line-height: 26px;
        text-align: center;
        display: inline-block;
    }
    .modal-content{
        background: #ffffff none repeat scroll 0 0;
        min-width: 1000px;
        max-width: 100%;
        margin-top: 50px;             
    }
    .modal-body{
        padding: 60px;
    }
}
.name{
    font-weight: 500;
    font-size: 1rem;
    margin: 0 0 5px;
}
.product-block{
    position:relative;
    margin-bottom: 1.25rem;
    @media(min-width: 1200px){
        margin-bottom: $theme-margin;
    }
    .sale-perc{
        background: #fd5f5c;
        color: #ffffff;
        font-size: 14px;
        font-weight: 400;
        padding: 0 5px;
        line-height: 1.7;
        position: absolute;
        left: 12px;
        text-transform: uppercase;

        top: 12px;
        z-index: 8;
    }
    .out-of-stock{
        background: darken(#e1e1e1, 5%);
        color: #fff !important;
        font-size: 14px !important;
        font-weight: 400;
        padding: 0 8px;
        position: absolute;
        right: 12px;
        text-transform: uppercase;
        font-family: $font-family-second;
        top: 12px;
        z-index: 8;  
    }
    .image{
        position: relative;
        margin: 0;
        .downsale{
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
            position: absolute;
            right:0;
            top:0;
            z-index: 8;
            padding:2px 10px;
            @include border-radius(2px);
            background:#d42e2e;
            color: #fff;
        }
        img{
            display: inline-block;
            @include transition(all 0.5s ease-in-out 0s);
        }
        .image-effect{
            top: 0;
            position: absolute;
            left:50%;
            @include translateX(-50%);
            z-index: 2;
            @include opacity(0);
        }  
        .image-no-effect{
            @include scale(1);
        } 
    }
    .block-inner{
        overflow: hidden;
        @include border-radius($border-radius);
        &:hover{
            .image{
                .image-hover{
                    @include opacity(0);
                }
                .image-effect{
                    @include opacity(1);
                } 
            }
        }
        &.text-center{
            .image{
                img{
                    margin:auto;
                }
                .image-effect{
                    left:50%;
                    @include translateX(-50%);
                } 
            } 
        }
    }
    .product-image{
        position:relative;
        display:block;
    }
    .rating{
        margin-bottom: 3px;
        .counter{
            font-size: 13px;
        }
    }
    .add-cart{
        margin-top: 12px;
        a{
            background: #fff;
            color: $body-link;
            border-color: $theme-color;
            @media(min-width: 1200px){
                min-width: 180px;
                text-align: center;
            }
            &.added_to_cart,
            &:hover,&:focus{
                background-color: $theme-color;
                border-color: $theme-color;
                color: #fff;
            }
        }
    }
    .name{
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden
    }
    .metas{
        padding: 14px;
    }
    .categories{
        margin-bottom: 5px;
        a{
            font-size: 14px;
            color: $body-color;
            &:hover,&:focus{
                color: $theme-color;
            }
        }
    }
    &:hover{
        .add-cart a{
            background-color: $theme-color;
            border-color: $theme-color;
            color: #fff;
        }
    }
}
.product-block-search{
    padding: 1rem;
    .entry-title{
        margin: 0 0 5px;
        font-size: 1.125rem;
        @media(min-width: 1200px){
            font-size: 1.375rem;
        }
    }
    .add-cart{
        margin-top: 10px;
        position: static;
        visibility: visible;
        @include opacity(1);
        @include translateY(0);
        a{
            width: auto;
        }
    }
    .entry-thumb{
        overflow: hidden;
        @include border-radius($border-radius);
        margin: 0;
    }
    .col-content{
        @media(max-width: 767px){
            margin-top: 10px;
        }
    }
}
// single product
.woocommerce .woocommerce-product-rating{
    .star-rating{
        margin: 0;
        display: inline-block;
        float: none;
        vertical-align: middle;
    }
    .woocommerce-review-link{
        display: inline-block;
        font-size:14px;
        line-height:1;
    }
}
.woocommerce #content div.product div.summary, .woocommerce div.product div.summary, .woocommerce-page #content div.product div.summary, .woocommerce-page div.product div.summary,
.woocommerce #content div.product div.images, .woocommerce div.product div.images, .woocommerce-page #content div.product div.images, .woocommerce-page div.product div.images{
    width: 100%;
}
.single_variation_wrap{
    div.qty{
        font-size: 15px;
        text-transform: uppercase;
        color: $body-color;

        margin-top: 10px;
        margin-right: 10px;
    }
}
.wrapper-shop{
    @media(min-width:1024px){
        padding-top:50px;
        padding-bottom:50px;
    }
    .apus-pagination{
        border-top:1px solid $border-color;
        padding-top: 40px;
        margin-top: 0;
    }
    aside.sidebar{
        background: transparent;
    }
}
.thumbnails-image{
    ul{
        list-style: none;
        margin:0;
        padding:0;
    }
    .prev,
    .next{
        display: block;
        width: 100%;
        text-align:center;
        font-size: 18px;
        color: #000;
    }
    .thumb-link{
        display: block;
        @include opacity(0.4);
        margin: 10px 0;
        &:hover,
        &.active{
            @include opacity(1);
        }
    }
}
.details-product{
    @media(min-width:1200px){
        .left-detail{
            padding-right: 0;
        }
    }
    .shipping_info{
        margin-top: 15px;
        @media(min-width:1200px){
            margin-top: 40px;
        }
        @include transition(all 0.3s ease-in-out 0s);
        &:hover{
            color: $body-color;
        }
        ul{
            list-style: none;
            padding:0;
            margin:0;
            i{
                margin-right: 5px;
            }
            li{
                margin-bottom:0px;
                @media(min-width: 1200px){
                    margin-bottom:5px;
                }
                &:last-child{
                    margin-bottom: 0;
                }
            }
        }
    }
    .price-rating-wrapper{
        margin-top: 10px;
        @media(min-width: 1200px){
            margin-top: 20px;
        }
        clear: both;
        overflow: hidden;
        .price{
            margin-right: 15px !important;
            line-height: 1.4;
            del{
                display: block !important;
            }
        }
        > *{
            display: inline-block;
            vertical-align: bottom;
        }
    }
    .pro-info{
        @media(min-width: 1200px){
            font-size: 30px;
        }
        margin: 0 0 20px;
    }
    .popup-video{
        background:#fff;
        height: 40px;
        line-height: 40px;
        min-width: 40px;
        overflow: hidden;
        display: inline-block;
        @include box-shadow(0 0 10px 0 rgba(0, 0, 0, 0.2));
        @include border-radius(50px);
        @include transition(all 0.3s ease-in-out 0s);
        @include flexbox;
        align-items: center;
        -webkit-align-items: center; /* Safari 7.0+ */
        flex-direction:row;
        -webkit-flex-direction:row;
        i{
            height: 40px;
            line-height: 40px;
            width: 40px;
            font-size: 13px;
            text-align: center;
            text-indent: 3px;
        }
        span{
            @include transition(all 0.3s ease-in-out 0s);
            white-space:nowrap;
            max-width: 0;
            padding: 0;
            overflow: hidden;
        }
        &:hover{
            span{
                max-width: 280px;
                padding-right: 12px;
            }
        }
    }
    .product-cat{

        text-transform: uppercase;
        letter-spacing: 2px;
        font-size: 12px;
        a{
            color: $theme-color;
        }
    }
    // tab
    div.video{
        z-index: 8;
        position:absolute;
        left: 10px;
        bottom:10px;
        @media(min-width: 768px){
            left: 20px;
            bottom:20px;
        }
    }
    .apus-countdown {
        margin-top: 5px;
    }
    .special-product{
        padding:8px 0;
    }
    .apus-countdown .times{
        > span{
            color: $theme-color;
            margin-bottom:5px;
        }
        margin-bottom: 5px;
        > div{
            text-align: center;
            vertical-align: middle;
          min-width: 40px;
          font-size: 12px;
          display: inline-block;
          font-weight: 400;
          text-transform: uppercase;
          margin:0 5px;
          padding:8px;
          &:first-child{
            margin-left: 0;
          }
          span{
            font-weight: 500;
            margin-bottom:5px;
            @include border-radius(3px);
            font-size: 18px;
            display: block;
            color: $body-link;
          }
        }
    }
    .apus-woocommerce-product-gallery-thumbs{
        .slick-slide{
            &:hover,
            &:active,
            &.slick-current{
                .thumbs-inner{
                    @include opacity(1);
                    border-color: #1A3454;
                }
            }
            .thumbs-inner{
                @include transition(all 0.2s ease-in-out 0s);
                max-width:100%;
                display: block;
                cursor: pointer;
                position: relative;
                @include opacity(0.7);
                @include border-radius($border-radius);
                overflow: hidden;
                border: 1px solid transparent;
                &:hover{
                    @include opacity(1);
                    border-color: #1A3454;
                }
            }
        }
        // fix for position
        &.vertical{
            margin:0;
            .slick-slide{
                padding:0;
                margin-bottom: 10px;
                border:none;
            }
            .slick-arrow{
                text-align: center;
                background-color:transparent !important;
                border:none !important;
                @include box-shadow(none !important);
                i{
                    @include size(30px,30px);
                    background-color:#fff;
                    @include border-radius(50%);
                    @include box-shadow(0 0 1px 1px rgba(0, 0, 0, 0.2));
                    line-height: 30px;
                    display: inline-block;
                    @include transition(all 0.2s ease-in-outs 0s);
                }
                &:hover,&:focus{
                    i{
                        color: #fff;
                        background-color:$theme-color;
                        @include box-shadow(none);
                    }
                }
            }
            .slick-prev{
                top: -40px;
                bottom:100%;
                @include translate(0,-5px);
                width: 100%;
                left:0;
                font-size: 11px;
            }
            .slick-next{
                width: 100%;
                top: 100%;
                bottom:inherit;
                @include translate(0,0);
                right:0;
                font-size: 11px;
            }
        }
    }
    .image-mains{
        &.thumbnails-bottom{
            .wrapper-thumbs {
                margin-top: 10px;
            }
            .slick-carousel{
                margin-left: -5px;
                margin-right: -5px;
                .slick-slide{
                    padding-left:5px;
                    padding-right:5px;
                }
            }
        }
        &.thumbnails-left{
            .apus-woocommerce-product-gallery-wrapper{
                width:calc(100% - 100px);
                @media(min-width: 1200px){
                    width:calc(100% - 160px);
                }
                float:right;
            }
            .wrapper-thumbs{
                float:left;
                width:100px;
                padding-right:20px;
                @media(min-width: 1200px){
                    padding-right:30px;
                    width:160px;
                }
            }
            @media(max-width:767px){
                .apus-woocommerce-product-gallery-wrapper{
                    width:calc(100% - 70px);
                }
                .wrapper-thumbs{
                    width:70px;
                    padding-right:10px;
                }
            }
        }
        &.thumbnails-right{
            .apus-woocommerce-product-gallery-wrapper{
                width:calc(100% - 160px);
                float:left;
            }
            .wrapper-thumbs{
                float:right;
                width:160px;
                padding-left:20px;
                @media(min-width: 1200px){
                    padding-left:30px;
                }
            }
            @media(max-width:767px){
                .apus-woocommerce-product-gallery-wrapper{
                    width:calc(100% - 70px);
                }
                .wrapper-thumbs{
                    width:70px;
                    padding-left:10px;
                }
            }
        }
    }
    .description{
        .title{
            font-size:21px;
        }
    }
    .apus-woocommerce-product-gallery-wrapper{
        position: relative;
        @include border-radius($border-radius);
        overflow: hidden;
        .downsale{
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
            position: absolute;
            left:0;
            top:0;
            z-index: 9;
            padding:2px 10px;
            @include border-radius(0);
            background:#d42e2e;
            color: #fff;
        }
        .apus-woocommerce-product-gallery {
            margin:0;
            overflow: hidden;
            @include border-radius(4px);
            .slick-slide{
                padding:0;
            }
        }
        .woocommerce-product-gallery__trigger{
            position: absolute;
            z-index: 2;
            top: 10px;
            right: 10px;
            display: inline-block;
            @include size(35px,35px);
            line-height: 35px;
            font-size: 15px;
            @media(min-width: 1200px){
                top: 20px;
                right: 20px;
                @include size(45px,45px);
                line-height: 45px;
                font-size: 21px;
            }
            text-align: center;
            border:0;
            @include border-radius(50%);
            @include transition(all 0.3s ease-in-out 0s);
            color:$body-link;
            background: #fff;
            &:hover,&:active{
                background: $theme-color;
                color: #fff;
            }
        }
        &:hover{
            .woocommerce-product-gallery__trigger{
                @include opacity(1);
            }
        }
    }
    .woocommerce-product-details__short-description{
        &.hideContent{
            overflow: hidden;
            height: 60px;
            @include transition(all 0.2s ease-in-out 0s);
        }
    }
    .woocommerce-variation-add-to-cart{
        width: 100%;
        overflow: hidden;
    }
    .list{
      li{
        margin-bottom: 10px;
      }
      i{
        color: $theme-color;
        margin-right: 8px;
      }
    }
    .woocommerce-variation-price{
        margin-bottom: 15px;
    }
    .product_meta{
        overflow:hidden;
        clear:both;
        > span{
            display: block;
        }
        .sub_title{
            display: inline-block;
            color: $body-link;
        }
        a{
            color: $body-color;
            &:hover,&:focus{
                color: $body-link;
            }
        }
    }
    .information{
        @media(min-width: 992px){
            padding-left: 30px;
        }
        @media(min-width: 1200px){
            padding-left: 90px;
        }
        .summary{
            float: none !important;
            width: 100%;
            margin: 0 !important;
        }
        .single_variation_wrap{
            padding-top:10px;
        }
        .price{
            margin: 0.4rem 0 0.625rem;
            @media(min-width: 1200px){
                margin: 0.4rem 0 1rem;
            }
            &,
            ins{
                font-size: 1.2rem !important;
                @media(min-width: 1200px){
                    font-size: 24px !important;
                }
                color: $theme-color !important;
            }
            del{
                font-size: 1rem !important;
            }
        }
        .woocommerce-product-rating{
            margin-bottom: 0 !important;
        }
        .woocommerce-product-details__short-description{
            margin-bottom:$theme-margin / 2;
            @media(min-width:1200px){
                margin-bottom: 35px;
            }
            p:last-child{
                margin-bottom: 0;
            }
            ul{
                list-style: none;
                padding:0;
                margin:0;
                li{
                    margin-bottom: 5px;
                    &:last-child{
                        margin-bottom: 0;
                    }
                }
            }
        }
        .view-more-desc {
            font-size: 14px;
            color: #b7b7b7;
            @include transition(all 0.2s ease-in-out 0s);
            &:hover{
                color: $body-link;
            }
            &.view-less{
                color: $danger;
            }
        }
        .woocommerce-product-details__short-description-wrapper.v2{
            margin-bottom: 15px;
            @media(min-width: 1200px){
                margin-bottom: 30px;
            }
            .woocommerce-product-details__short-description{
                margin-bottom: 3px;
            }
        }
        .cart{
            width:100%;
            margin:0 0 15px !important;
            @media(min-width: 1200px){
                margin: 0 0 35px !important;
            }
            .group_table{
                tr{
                    td:first-child{
                        div.quantity{
                            margin:0 !important;
                        }
                    }
                }
                ~ .button{
                    margin-left: 0;
                }
            }
            div.quantity-wrapper{
                overflow: hidden;
                margin:0;
                float: left;
                > *{
                    display: inline-block;
                    vertical-align: middle;
                    float: none !important;
                }
                > label{
                    font-weight: 400;
                    margin-right: 0.625rem;
                    margin-bottom: 0;
                }
                .qty{
                    background: #fff;
                }
            }
            .button{
                margin-left: 10px;
                @media(min-width: 1200px){
                    margin-left: 20px;
                    min-width: 180px;
                    text-align: center;
                }
            }
            .quantity.hidden{
                + .button{
                    margin:0;
                }
            }
            &.grouped_form{
                .quantity-wrapper{
                    margin:0 !important;
                    label{
                        display: none;
                    }
                }
            }
        }
        .clear{
            display: none;
        }
        .product_title{
            clear: both;
        }
    }
    .title-cat-wishlist-wrapper{
        position:relative;
        padding-right: 30px;
        margin-bottom:20px;
        @media(min-width: 1200px){
            margin-bottom: 30px;
        }
    }
    // social
    .apus-social-share{
        margin-top: 15px;
        span{
            font-size:15px;
            display:inline-block;
            margin-right: 10px;
        }
        a{
            width: 35px;
            height: 35px;
            line-height: 35px;
            font-size: $font-size-base;
            margin: 0 2px;
            &:hover,&:active{
                background-color: $theme-color;
                color: #fff;
            }
        }
    }
    // discount
    .apus-discounts{
        margin:20px 0 15px;
        padding:15px 20px;
        background: #eceff6;
        font-size: 13px;
        ul{
            margin:0;
            list-style: none;
            padding:0;
            li{
                margin: 0 0 3px;
                &:before{
                    font-family: 'Font Awesome 5 Free';
                    font-weight: 900;
                    color:$theme-color;
                    content: "\f00c";
                    margin-right: 8px;
                }
            }
        }
        .icon{
            display: inline-block;
            vertical-align: middle;
            @include size(35px,35px);
            text-align: center;
            line-height: 35px;
            color: #fff;
            background: darken(#eceff6,20%);
            font-size: 14px;
            @include border-radius(50%);
            margin-right: 10px;
        }
        .title{
            font-size: 18px;
            margin:0 0 10px;
        }
    }
    .product-free-gift{
        margin:0 0 20px;
        padding:15px 20px;
        background: $danger;
        .icon{
            display: inline-block;
            vertical-align: middle;
            @include size(35px,35px);
            text-align: center;
            line-height: 35px;
            color: #fff;
            background: #e23e1d;
            font-size: 14px;
            @include border-radius(50%);
            margin-right: 10px;
        }
        .title{
            font-size: 18px;
            margin:0 0 10px;
        }
        .list-gift{
            font-size: 13px;
            list-style: none;
            padding:0;
            margin:0;
            li{
                margin-bottom: 3px;
            }
            i{
                color: #e23e1d;
            }
        }
        .hightcolor{
            font-weight: 500;
            color: #e23e1d;
        }
    }
}
.related.products{
    padding:$theme-padding 0 0;
    @media(min-width: 1200px){
        padding: 80px 0 0;
    }
    .widget-title{
        text-align: center;
        font-size: 20px;
        margin:0 0 $theme-margin;
        @media(min-width: 1200px){
            font-size: 30px;
            margin-bottom: 50px;
        }
    }
    .product-block{
        margin-bottom: 0;
    }
}
.accessoriesproducts-wrapper{
    position: relative;
    &.loading:before{
        position: absolute;
        @include size(100%,100%);
        top: 0;
        left: 0;
        z-index: 99;
        content: '';
        background:url('#{$image-theme-path}loading-quick.gif') center center no-repeat rgba(255,255,255,0.9);
    }
}
/*------------------------------------*\
    Product Category and Subcategories
\*------------------------------------*/
.product-category{
    .product-category-content{
        position: relative;
        overflow: hidden;
        min-height: $product-category-content-height;
        margin: $product-category-content-margin;
    }
    .product-category-image{
        display: block;
    }
    .product-category-title{
        text-transform: none;
        position: absolute;
        text-align: center;
        bottom: 0;
        left: 0;
        width: 100%;
        font-weight: $product-category-title-font-weight;
        font-size: $product-category-title-font-size;
        color: $product-category-title-color;
        margin: $product-category-title-margin;
        padding: $product-category-title-padding;
        background: rgba($product-category-title-bg, .3);
        .count{
            background: transparent;
            color: $product-category-title-color;
        }
    }
}

/*------------------------------------*\
    Quickview
\*------------------------------------*/
#apus-quickview-modal{
    .product_meta{
        margin: (15px) 0 0;
    }
}

/**
 *
 *  Woocommerce Form
 */
.form-row {
	.checkbox, .input-radio{
		margin-bottom: 0;
		margin-top: 0;
	}
}	
.woocommerce form .form-row{
    margin: 0 0 15px;
    padding:0;
}
.woocommerce .col2-set .col-2, .woocommerce-page .col2-set .col-2,
.woocommerce .col2-set .col-1, .woocommerce-page .col2-set .col-1{
    width: 100%;
}
/* End
------------------------------------------------*/

/*-------------------------------*\
    Utilities
\*------------------------------------*/
.woocommerce #reviews #comments ol.commentlist{
    padding:0;
}
//reviews
.woocommerce #reviews #comments ol.commentlist li{
    margin:0 0 20px;
    @media(min-width: 1200px){
        margin-bottom: 40px;
    }
    .apus-avata{
        min-width: 80px;
        padding-right: 10px;
        @media(min-width: 1200px){
            min-width: 90px;
            padding-right: 20px;
        }
        .apus-image{
            display: inline-block;
        }
    }
    img.avatar{
       @include size(70px, 70px);
       border:none;
       @include border-radius(50%);
       padding: 0;
       margin:0;
       position: relative; 
    }
    .dokan-review-author-img{
        float: left;
        padding-right: 30px;
    }
    .comment-text{
        border:none;
        padding:15px 0 0;
        margin:0;
    }
    .description{
        margin-top: 10px;
        p{
            margin:0;
        }
    }
    .apus-author{
        font-size: $font-size-base;
        color: $body-link;
        margin:0;
        text-transform: capitalize;
    }
    .content-comment {
        margin-top:12px;
        p:last-child{
            margin-bottom: 0;
        }
    }
}
#respond {
    [for="rating"]{
        font-weight: 400;
        margin:0;
        margin-right: 0.9375rem;
    }
	.form-submit {
		input {
            left: auto;
		}
	}
}
.woocommerce #reviews{
    #comment {
        height: 150px;
        @media(min-width: 1200px){
            height: 200px;
        }
        resize: none;
    }
    .comment-reply-title{
        margin-bottom: 15px;
    }
}
/*------------------------------------*\
    Quantity inputs
\*------------------------------------*/
.woocommerce .quantity .qty{
    @include transition(all 0.2s ease-in-out 0s);
    appearance: textfield;
    -moz-appearance: textfield;
    -webkit-appearance: textfield;
    width: 75px;
    @include border-radius($border-radius);
    border:1px solid $input-border-color;
    padding: $input-padding-y $input-padding-x;
    color: $body-color;
    background-color: #fff;
    outline: none;
    &::-webkit-input-placeholder { /* Edge */
      @include opacity(1);
    }
    &:-ms-input-placeholder { /* Internet Explorer 10-11 */
      @include opacity(1);
    }
    &::placeholder {
      @include opacity(1);
    }
    &:focus{
        border-color: $input-focus-border-color;
    }
}
.woocommerce .quantity{
    .reader-text{
        font-size: 14px;
        font-weight: 400;
        margin-bottom: 0;
        margin-right: 10px;
    }
}
.woocommerce a.remove{
    @include border-radius(0);
}
/*------------------------------------*\
    Forms
\*------------------------------------*/
.form-row {
	@include clearfix();
    label.hidden {
        visibility:hidden;
    }
    label.inline {
        display: inline;
    }
    label{
    	display: block;
        font-weight: 500;
        color: $body-link;
        font-size: 1rem;
    }
    select {
        cursor: pointer;
    }
    .required {
        color: $red;
        font-weight: $headings-font-weight;
        border: 0;
    }
    .input-text{
    	width: 100%;
    	padding: 5px 18px;
    }
    &.form-row-first{
    	width: 47%;
    	float: left;
    }
    &.form-row-last{
    	width: 47%;
    	float: right;
    }
    &.form-row-wide{
    	clear: both;
    }
}
.select2-container .select2-choice{
    padding:5px 7px;
}
/*------------------------------------*\
    Mini cart and wishlist
\*------------------------------------*/
.total-minicart{
    color: $body-link;
    font-weight:normal;
    font-size: 16px;
    margin-left: 5px;
    display: inline-block;
}
.mini-cart{
    padding: 0 10px;
}
.wishlist-icon,
.mini-cart{
    display: inline-block;
    position:relative;
    color:$body-link;
    i{
        font-size: 20px;
        line-height: 1;
        display: inline-block;
        vertical-align: text-top;
        margin:0 !important;
    }
    &:after{
        display: none !important;
    }
    .count{
        position:absolute;
        top: -5px;
        right: 2px;
        display: inline-block;
        font-size: 10px;
        color: #fff;
        background:$theme-color;
        @include border-radius(50%);
        line-height: 15px;
        min-width: 15px;
        padding:0 3px;
        text-align: center;
    }
}
.wishlist-icon{
    i{
        margin-right: 5px;
    }
}
/*------------------------------------*\
    Star Ratings
\*------------------------------------*/
.woocommerce p.stars{
    font-size: 12px;
    letter-spacing: 3px;
    margin: 0 !important;
    height: 12px;
    a:before{
        content: "\53";
        font-family: 'star';
        color: $star-rating-active-color;
        @include transition(all 0.1s ease-in-out 0s);
    }
    &:hover{
        a:before{
            content: "\53";
            font-family: 'star';
        }
    }
    a:hover{
        ~ a:before{
            content: "\53";
            color: $star-rating-color;
        }
    }
    &.selected{
        a:not(.active)::before{
            content: "\53";
        }
        a.active{
            &:before{
                content: "\53";
            }
            ~ a:before{
                content: "\53";
                color: $star-rating-color;
            }
        }
    }
}
.woocommerce {
    .star-rating{
        overflow: hidden;
        position: relative;
        @include size(80px, 11px);
        line-height: 11px;
        font-family: 'star';
        font-size: 11px;
        letter-spacing: 5px;
        float: none;
        &:before {
            content: "\53\53\53\53\53";
            color: $star-rating-color;
            /*rtl:ignore*/
            float: left;
            top: 0;
            left: 0;
            position: absolute;
        }
        span {
            overflow: hidden;
            /*rtl:ignore*/
            float: left;
            top: 0;
            left: 0;
            position: absolute;
            padding-top: 1.5em;
        }
        span:before {
            content: "\53\53\53\53\53";
            top: 0;
            position: absolute;
            left: 0;
            color: $star-rating-active-color;
        }
    }
}
.rating{
    > *{
        display: inline-block !important;
        vertical-align: middle;
    }
    .star-rating{
        margin: 0 !important;
    }
    .counts{
        color: $body-link;
        margin-left: 7px;
    }
}
/*------------------------------------*\
    Filter
\*------------------------------------*/
.archive-shop{
    .page-title{
        display: none;
    }
}
.show-filter{
    font-size:18px;
    color:$theme-color;
    cursor:pointer;
    font-weight:400;
    text-transform:uppercase;
    letter-spacing:1px;
    @include transition(all 0.2s ease-in-out 0s);
    &:hover,&:active{
        color:$theme-color;
    }
    i{
        margin-left: 10px;
    }
}
.apus-shop-menu{
    font-size: 15px;
    margin:0;
    position:relative;
    .filter-action{
        i{
            margin-right: 3px;
        }
    }
    ul.apus-filter-menu{
        padding:0;
        margin:5px 0 0;
        list-style: none;
        float: right;
        li{
            display: inline-block;
        }
    }
    ul.apus-categories{
        float: left;
        padding:0;
        margin:2px 0 0;
        list-style: none;
        li{
            display: inline-block;
            margin-right: 40px;
            a{
                text-transform: capitalize;
                padding:0;
                font-size: 16px;
                font-weight:500;
                color:$body-link;
                position:relative;
                display:inline-block;
            }
            .product-count{
                font-size: 14px;
                color: $body-color;
                margin: 0 2px;
                vertical-align: top;
                display: inline-block;
            }
            &.current-cat{
                > a{
                    color:$theme-color;
                }
            }
        }
        .apus-shop-sub-categories{
            padding:0px;
            margin: 10px 0 0;
            li{
                a{
                    font-size: 16px;
                }
            }
        }
    }
    .content-inner{
        #apus-orderby{
            margin-left: 40px;
        }
    }
}

.apus-categories-dropdown{
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    border:none;
    color: $body-link;
    font-size: 14px;
    margin-top: 4px;
    .category-dropdown-label{
        cursor: pointer;
    }
    option {
        font-size: 16px;
        color: $body-color;
        &[selected="selected"]{
            color: $body-link;
        }
    }
    .dropdown-menu{
        min-width: 200px;
        padding:20px 30px;
        @include border-radius(0);
        border:1px solid $theme-color;
        @include box-shadow(none);
        ul{
            list-style:none;
            padding:0;
            margin:0;
            li{
                margin: 0 0 5px;
                a{
                    color: $body-color;
                    &:hover,&:active{
                        color: $body-link;
                    }
                }
                &.active{
                    color: $body-link;
                }
                &:last-child{
                    margin: 0;
                }
            }
        }
    }
}
.before-shop-header-wrapper{
    position:relative;
    @media(min-width:768px){
        .before-shop-loop-fillter{
            position:absolute;
            top:20px;
        }
    }
}
.pagination-top{
    margin-top:-6px;
    .apus-pagination.pagination-woo{
        margin:0;
    }
    .apus-pagination .apus-pagination-inner{
        padding:0;
    }
    &.has-fillter{
        .apus-pagination .apus-pagination-inner{
            padding:0 $theme-margin;
        }
    }
}
.apus-filter{
    margin-bottom: 15px;
    font-size: 14px;
    @media(min-width: 1200px){
        margin-bottom: $theme-margin;
    }
    .shop-page-title{
        margin-top:0;
        margin-bottom:0;
        font-size: 24px;
    }
    .woocommerce-result-count{
        margin:0;
        color: $body-link;
        font-size: 14px;
        font-weight: 500;
    }
    #apus-orderby{
        float: left;
    }
    .woocommerce-ordering{
        margin:0;
    }
    .subtitle{
        font-weight: 500;
        color: $body-link;
        margin-right: 12px;
    }
    select{
        color: $body-color;
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        background: url("#{$image-theme-path}select.png") #EEF2F6 right 10px center no-repeat;
        padding: 10px 12px;
        margin:0;
        border:1px solid #EEF2F6;
        @include border-radius($border-radius);
    }
    // select 2
    .select2-container--default.orderby{
        min-width: 180px;
    }
    .select2-selection--single{
        border:0 !important;
    }
    .display-mode{
        margin-top: 4px;
    }
    .change-view{
        color: #cccccc;
        i{
            font-size: 24px;
            vertical-align: middle;
        }
        display: inline-block;
        + .change-view{
            margin-left: 10px;
            @media(min-width: 1200px){
                margin-left: 20px;
            }
        }
        &:hover,
        &.active{
            color: $theme-color;
        }
    }
    .form-educrat-ppp{
        .educrat-wc-wppp-select{
            @media(min-width: 1200px){
                min-width: 190px;
            }
        }
    }
}
// show
.form-educrat-ppp{
    float: left;
    select{
        font-size: 16px;
        color: $body-color;
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        background: url("#{$image-theme-path}select.png") #fff right 10px center no-repeat;
        font-weight: 400;
        border:1px solid $border-color;
        padding:3px 20px;
        @include border-radius(2px);
        margin:0;
        border:1px solid $border-color;
    }
}
#apus-orderby{
    .orderby-label{
        color: $body-color;
        display: inline-block;
        font-size:14px;
        font-weight: 300;
        cursor: pointer;
        border:1px solid $border-color;
        @include border-radius(50px);
        padding:4px 15px;
    }
    .dropdown-menu{
        min-width: 200px;
        padding:20px 30px;
        @include border-radius(5px);
        @include border-radius(0);
        border:1px solid $theme-color;
        @include box-shadow(none);
        ul{
            list-style:none;
            padding:0;
            margin:0;
            li{
                margin: 0 0 5px;
                a{
                    color: $body-color;
                    &:hover,&:active{
                        color: $body-link;
                    }
                }
                &.active{
                    color: $body-link;
                }
                &:last-child{
                    margin: 0;
                }
            }
        }
    }
}
/*------------------------------------*\
    Mini Cart
\*------------------------------------*/
.apus-topcart{
	.dropdown-menu{
        margin: 8px 0 0 !important;
        padding: 15px;
		width: 300px;
        @media(min-width: 1200px){
            width: 410px;
            padding: $theme-padding;
        }
        border: 1px solid $border-color;
        @include border-radius($border-radius);
        @include box-shadow(0 25px 70px 0 rgba(#01213A,0.07));
        background: #fff;
        @include opacity(0);
        &.show{
            display: block;
            @include opacity(1);
        }
        &:before{
            @include rotate(45deg);
            background-color: #ffffff;
            content: "";
            width: 10px;
            height: 10px;
            right: 15px;
            top: -5px;
            position: absolute;
        }
	}
    .buttons{
        margin: 0;
        .wc-forward{
            width: calc(50%-10px);
            + .wc-forward{
                margin-left: 20px;
            }
        }
    }
    .overlay-offcanvas-content{
        background:rgba(0,0,0,0.5);
        position:fixed;
        top:0;
        left:0;
        @include size(100%,100%);
        @include opacity(0);
        @include transition(all 0.3s ease-in-out 0s);
        cursor: no-drop;
        @include translateX(-30px);
        visibility: hidden;
        z-index: 2;
        &.active{
            visibility: visible;
            @include opacity(1);
            @include translateY(0);
        }
    }
    .offcanvas-content{
        z-index: 3;
        position:fixed;
        right:0;
        top:0;
        background:#fff;
        @include transition(all 0.35s ease-in-out 0s);
        @include opacity(0);
        width:400px;
        height: 100vh;
        @include translateX(100%);
        &.active{
            @include opacity(1);
            @include translateY(0);
        }
        .shopping_cart_content .cart_list{
            max-height: calc(100% - 180px);
        }
        .title-cart-canvas{
            font-size: 16px;
            text-align: center;
            margin:0 0 10px;
            padding:10px;
            border-bottom:1px solid $border-color;
            text-transform: uppercase;
            position:relative;
            .close-cart{
                position:absolute;
                top:11px;
                left: 14px;
                z-index: 1;
                background:#fff;
                font-size: 18px;
                cursor: pointer;
                color: $danger;
            }
        }
        .shopping_cart_content{
            padding:10px;
            @media(min-width: 1200px){
                padding:15px 15px 30px;
            }
            height: calc(100vh - 50px);
            display: -webkit-flex; /* Safari */
            display: flex;
            flex-wrap:wrap;
            -webkit-flex-wrap: wrap;
            .cart-bottom{
                align-self:flex-end;
                -webkit-align-self:flex-end;
                width:100%;
            }
            .cart_list {
                width:100%;
            }
        }
    }
}
.shopping_cart_content{
    font-size: 14px;
    .variation{
        margin:0 0 3px;
        overflow: hidden;
        dt{
            margin-right: 5px;
        }
        dt,dd{
            float: left;
            p{
                margin: 0;
            }
        }
    }
    .cart_list{
        padding:0 0 10px;
        max-height: 400px;
        overflow-y: auto;
        scrollbar-width: thin;
        > div{
            margin: 0 0 20px;
            overflow:hidden;
            &.empty{
                border:none;
                margin:0;
                color: $body-link;
            }
            &:last-child{
                border:none;
            }
        }
        .image{
            @include size(80px,80px);
            display: block;
            @include border-radius($border-radius);
            overflow: hidden;
            img{
               @include size(80px,80px);
                max-width: none;
            }
        }
        .quantity{
            color:$body-link;
            font-weight: 500;
        }
        .name{
            font-weight: 400;
            font-size: $font-size-base;
            margin: 0 0 5px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        .cart-item{
            margin: 0;
            font-size: 16px;
        }
        .cart-main-content{
            text-align: left;
            position: relative;
            padding-left: 15px;
            .remove{
                position: absolute;
                right: 0;
                top: 6px;
                z-index: 1;
                font-size: 12px;
                background:transparent !important;
                color:$theme-color !important;
                &:hover,&:focus{
                    color: $danger !important;
                }
                i:before{
                    font-weight: 700;
                }
            }
        }
        .cart-item{
            overflow:hidden;
        }
    }
    .total{
        color: $headings-color;
        border-top:1px solid $border-color;
        position: relative;
        margin: 0;
        font-weight: 500;
        text-transform: capitalize;
        padding:20px 0;
        font-size:18px;
        strong {
            font-weight:500;
        }
        .amount{
            font-size: 18px;
            margin-left: auto;
        }
        &.empty{
            border:none;
            margin:0;
            padding-top:0;
            text-align: center;
        }
    }
}
.woocommerce a.remove{
    padding:0;
    margin: auto;
    color: $danger;
    background:transparent;
}
/** Plugins  add to wishlist, compare **/
.place-order{
	padding: $theme-margin;
}
.input-text {
	border: 1px solid #e5e5e5;
	padding:  5px 10px;
}
.woocommerce{
	address{
		margin-bottom: 20px;
	}
}
.wc-block-product-categories{
    margin-bottom: 0;
}
.wc-block-product-categories-list,
.product-categories{
    list-style: none;
    margin: 0;
    padding:0;
    overflow:hidden;
    + .view-more-list-cat{
        position:absolute;
        background:#fff;
        bottom:1px;
        left:1px;
        width: calc(100% - 2px);
        z-index: 99;
        display: block;
        color: $success;
        padding:5px 54px 15px;
        &.view-less{
            color: $danger;
        }
        &:hover,&:active{
            text-decoration: underline;
        }
    }
    &.hideContent{
        height: 435px;
    }
    &.showContent{
        height: auto;
    }
    .children{
        list-style: none;
        padding:0;
    }
    li{
        li{
            padding-left: 20px;
        }
        padding: 0 0 8px;
        &:last-child{
            padding-bottom: 0;
        }
        &.current-cat-parent,
        &.current-cat,
        &:hover{
            > a{
                color: $link-hover-color;
            }
        }
        [class *="count"]{
            display: inline-block;
            float: right;
        }
    }
}
.top-archive-shop{
    padding-bottom:$theme-margin;
}
.add-cart{
    >.added{
        display: none !important;
    }
    .added_to_cart{
        &:after{
            display: none;
        }
    }
}
.apus-shop-products-wrapper{
    &.loading{
        position:relative;
        &:before{
            background: url('#{$image-theme-path}loading-quick.gif') center 100px / 50px  no-repeat rgba(#ffffff, 0.9);
            position: absolute;
            width: 100%;
            height: 100%;
            content: "";
            left: 0;
            top: 0;
            z-index: 99;
        }
    }
}
// my account
.woocommerce-account .woocommerce-MyAccount-content,
.woocommerce-account .woocommerce-MyAccount-navigation{
    width: 100%;
    float: none;
}
.woocommerce-account .woocommerce-MyAccount-navigation{
    border-bottom: 2px solid #eeeeee;
    .woocommerce-MyAccount-navigation-link{
        margin-right: 30px;
        display: inline-block;
        a{
            padding: 0 0 7px;
            position: relative;
            display: inline-block;
            &:before{
                @include size(100%,2px);
                background: $theme-color;
                position: absolute;
                bottom:-2px;
                left: 0;
                content: '';
                @include scale(0);
                @include transition(all 0.2s ease-in-out 0s);
            }
        }
        &.is-active,&:hover,&:active{
            > a{
                color: $theme-color;
                &:before{
                    @include scale(1);
                }
            }
        }
    }
}
.woocommerce-MyAccount-content{
    padding:20px 0; 
    h2{
        margin: 20px 0 10px;
        text-transform: uppercase;
        font-size: 18px;
        font-family: $font-family-second;
    }
}
.edit-account{
    br{
        display: none;
    }
    input[ type="text"],
    input[ type="password"]{
        height: 40px;
        @include border-radius(3px);
        &:focus{
            border-color: $border-color;
        }
    }
    legend{
        font-size: 72px;
        font-weight: 300;
        border:none;
        margin: 30px 0 0;
    }
    label{
        font-weight: normal;
        font-size: 16px;
        color: $body-link;
    }
}
.woocommerce-MyAccount-content,
.woocommerce-MyAccount-navigation{
    max-width: 970px;
    margin: auto;
}
.user{
    .title{
        font-size: 20px;
        margin:0 0 20px;
        text-align: center;
        @media(min-width: 1200px){
            font-size: 25px;
        }
    }
}
form.login,
form.register{
    margin: 0 !important;
    border:none !important;
    padding:0 !important;
    br{
        display: none;
    }
    label{
        font-weight: 400;
    }
    .form-control{
        padding: 5px 20px;
    }
    .form-group {
        margin: 0 0 20px;
        &:last-child{
            margin-bottom:0;
        }
    }
    .lost_password {
        a{
            text-decoration: underline;
        }
    }
    .action-group{
        font-size: 14px;
    }
    .input-text{
        background:#fff !important;
        border:1px solid $border-color !important;
        height: 40px;
        &:focus{
            border-color:darken($border-color, 10%) !important;
        }
    }
    input[ type="checkbox"]{
        margin-right: 7px;
    }
    .input-submit {
        ~ span{
            margin:10px 0 0;
            &.pull-left{
                margin-left: 15px;
            }
            &.lost_password{
                a{
                    color: $theme-color;
                }
            }
        }
    }
    .user-role{
        padding-left:20px;
        [type="radio"]{
            margin-top:11px;
        }
    }
}
.login-wrapper{
    .mfp-content{
        width:500px !important;
        max-width:80%;
        background-color:#fff;
    }
    .title{
        text-align: center;
    }
    .apus-mfp-close{
        font-size: 20px;
        display: inline-block;
        background:$danger;
        color: #fff;
        display: inline-block;
        @include size(42px,42px);
        line-height: 42px;
        border:none;
        margin:-21px;
        @include border-radius(50%);
        @include transition(all 0.3s ease-in-out 0s);
        @include opacity(0.9);
        &:hover,&:focus{
            @include opacity(1);
        }
    }
}
//cart
.cart_item{
    > .media-left{
        width: 70%;
    }
    img{
        width: 90px;
        max-width:none;
    }
    .content-left{
        overflow: hidden;
        padding-left: 20px;
    }
    .product-name{
        font-size: 15px;
        font-weight: 500;
        margin: 0 0 15px;
    }
    .price{
        font-size: 20px;
        color: #4c4c4c;
        font-weight: 400;
    }
    a.remove{
        margin: 0 0 15px;
        display: inline-block;
        color: $body-color !important;
        &:hover,&:active{
            color: $danger !important;
        }
    }
}
div.cart{
    .input-text {
        height: 53px;
        border:2px solid $border-color;
        &:focus,&:active{
            border-color:$body-link;
        }
    }
    label{
        font-size: 18px;
        color: #000;
    }
}
//order_review
.woocommerce{
    #order_review_heading{
        font-size: 18px;
        font-weight: 500;
        margin: 0 0 20px;
        @media(min-width: 1200px){
            font-size: 20px;
        }
    }
}
.woocommerce-order-details,
#order_review{
    table.woocommerce-table--order-details,
    table.woocommerce-checkout-review-order-table{
        border:none;
        margin-bottom: 20px;
        th,
        td{
            padding: 15px 0 !important;
            border:0;
        }
        thead{
            background-color: transparent;
            th{
                color: $body-link;
                font-size: 15px;
                font-weight: 500;
                border-bottom: 1px solid $border-color;
            }
        }
        tbody{
            td{
                width: 50%;
                padding: 7px 0 !important;
                font-size: 15px;
                font-weight: 400;
            }
            .product-quantity{
                font-weight: 400;
            }
            tr:first-child{
                td{
                    padding-top: 15px !important;
                }
            }
        }
        tfoot{
            th,td{
                border-top: 0;
                border-bottom: 1px solid $border-color !important;
                font-size: $font-size-base;
                font-weight: 500;
                color: $body-link;
            }
            tr:last-child{
                th,td{
                    border: 0 !important;
                    padding-bottom: 0 !important;
                }
            }
            .woocommerce-Price-amount{
                font-weight: 500;
            }
        }
    }
    .cart_item {
        margin:0;
        padding:0;
        border:none;
    }
    .product-name{
        margin: 0;
    }
    > .media-left {
        width: auto;
    }
    .subtotal{
        tr{
            > *{
                border-bottom:1px solid $border-color !important;
            }
        }
        th{
            border:none;
            font-weight: 400;
            color:$body-link;
        }
        td{
            text-align: right;
            padding:10px 0;
            font-weight: 400;
            label{
                font-weight: 400;
            }
        }
        .order-total{
             strong{
                font-size: 20px;
            }
        }
    }
    .order-total .amount{
        font-weight: 500;
    }
}
.wrapper-icon-completed{
    display: inline-block;
    text-align: center;
    color: #fff;
    background-color: $link-hover-color;
    @include size(50px,50px);
    line-height: 50px;
    @include border-radius(50%);
    font-size: 18px;
    @media(min-width: 1200px){
        @include size(80px,80px);
        line-height: 80px;
        font-size: 28px;
    }
}
.order-completed{
    margin:10px 0 5px;
    font-size: 22px;
    @media(min-width: 1200px){
        font-size: 30px;
        margin: 20px 0 10px;
    }
}
.woocommerce ul.order_details{
    margin:0;
    li{
        float: none;
        display: inline-block;
        font-size: $font-size-base;
        text-transform: inherit;
        width: 50%;
        margin:0 0 20px;
        float: left;
        border:0;
        padding:0;
        width: 50%;
        &:nth-child( 2 ) ~ li{
            margin-bottom: 0;
        }
        @media(min-width: 768px){
            width: 25%;
            margin:0;
        }
        strong{
            display: block;
            width: 100%;
            margin-top: 10px;
            font-weight: 500;
            color:$theme-color;
            font-size: 15px;
        }
    }
}

.product-top-title{
    position: relative;
    .view-more{
        position:absolute;
        top: 5px;
        right: 0;
    }
}
.layout-detail-product{
    #tabs-list-specifications{
        td{
            padding:15px;
            border-color:#eff0f2;
        }
        td:first-child{
            font-weight: 500;
            text-transform: uppercase;
        }
    }
}

.single-rating{
    margin:0 0 30px;
    padding:0 0 20px;
    border-bottom:1px solid $border-color;
    &:last-child{
        border:none;
        padding:0;
        margin: 0;
    }
    .avatar{
        max-width: none;
        min-width: 70px;
        @include border-radius(50%);
    }
    .media-left{
        padding-right: 20px;
    }
    .stars-value{
        float: right;
        .fa-star{
            color: #fednormal;
        }
    }
    h4{
        font-weight: 400;
        font-size: 10px;
        margin: 0 0 15px;
        color: $body-color;
        .name{
            font-weight: normal;
            font-size: 12px;
            color: #464646;
            text-transform: uppercase;
        }
    }
    h6{
        margin:0 0 15px; 
    }
}
//categories
.wrapper-filter{
    min-height:73px;
    position:relative;
    padding:20px 0;
    border-bottom:1px solid $border-color;
}
.shop-top-sidebar-wrapper{
    background:#fff;
    padding:20px 0 0;
    @media(min-width:992px){
        padding:40px 0 0;
    }
    display:block;
    overflow:hidden;
    width:100% !important;
    .dropdown{
        > span{
            color:#252525;
            font-weight:500;
            font-size:15px;
            display:block;
            margin:0 0 15px;
            text-transform:uppercase;
        }
    }
    .widget{
        margin-bottom:0;
    }
    @media(max-width:767px){
        margin-bottom:15px;
    }
    .shop-top-sidebar-wrapper-inner {
        margin-left:-15px;
        margin-right:-15px;
        > *{
            padding-left:15px;
            padding-right:15px;
            float: left;
            width:100%;
            @media(min-width:768px){
                width:20%;
            }
        }
    }
    .wrapper-limit{
        padding:10px;
        .apus-product-sorting,
        .apus-price-filter{
            padding:0;
            margin:0;
            list-style:none;
            li{
                margin-bottom:8px;
                &:last-child{
                    margin:0;
                }
            }
            a{
                white-space:nowrap;
            }
            .active,
            .current{
                color:$theme-color;
            }
        }
        .apus-product-sorting,
        .apus-price-filter,
        .woocommerce-widget-layered-nav-list{
            height:200px;
        }
    }
    .tagcloud {
        height:200px;
    }
}
// fix for shop banner
.products-wrapper-grid-banner{
    .cl-3,
    .cl-2{
        div.product{
            &.col-sm-4{
                &.first{
                    clear:none;
                }
                @media(min-width:768px){
                    &:nth-child(3n + 1){
                        clear:both;
                    }
                }
            }
        }
    }
    .col-md-cus-5{
        float: left;
        padding-left:15px;
        padding-right:15px;
        @media(min-width:992px){
            width:20%;
        }
    }
}
// categories
.product-category {
   h3{
    margin:15px 0 0;
    font-size: 18px;
    .count{
        background:transparent;
        padding:0;
    }
   } 
   .category-body{
        margin:0 0 20px;
        text-align: center;
        @media(min-width: 768px){
            margin:0 0 30px;
        }
   }
}
form.woocommerce-checkout{
    .select2-container--default.select2-container{
        .select2-selection--single{
            border-color: $border-color;
        }
        &.select2-container--open{
            .select2-selection--single{
                border-color: darken($border-color,5%);
            }
        }
    }
}
.wc-tab{
    > h2{
        font-size: 1rem;
        margin:0 0 1.25rem;
    }
    p:last-child{
        margin-bottom: 0;
    }
}
.woocommerce-tabs{
    margin-top: $theme-margin;
    max-width: 850px;
    margin-right: auto;
    margin-left: auto;
    @media(min-width: 1200px){
        margin-top: 80px;
    }
}
.details-product .image-mains{
    .slick-slide img{
        display: inline-block;
    }
}

.box-white-inner{
    background:#fff;
    border:1px solid $border-color;
    padding:20px;
    @include border-radius($border-radius);
    @media(min-width: 1200px){
        padding:$theme-margin;
    }
    &.box-order{
        margin-top: 20px;
        border: 2px dashed $theme-color;
        @media(min-width: 1200px){
            padding: 35px 50px;
            margin-top: 60px;
        }
    }
    &.order-details{
        margin-bottom: $theme-margin;
        background: #F7F8FB;
        @media(min-width: 1200px){
            padding:50px;
            margin-top: 30px;
            margin-bottom: 70px;
        }
    }
}
.woocommerce-customer-details > h2,
.woocommerce-order-details__title{
    font-size: 18px;
    font-weight: 500;
    margin:0 0 10px;
    @media(min-width: 1200px){
        font-size: 20px;
    }
}
.details-review{
    @media(max-width: 1199px){
        margin-top: $theme-margin;
    }
}
.order-review{
    background-color: #F7F8FB;
}
.woocommerce-noreviews{
    margin-bottom: 20px !important;
}
.apus-woocommerce-breadcrumb{
    background: #f7f8f9 !important;
}
.woocommerce-info{
    border-color: $theme-color;
    &:before{
        color: $theme-color;
    }
}